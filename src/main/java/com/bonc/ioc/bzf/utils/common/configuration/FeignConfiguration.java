package com.bonc.ioc.bzf.utils.common.configuration;

import com.bonc.ioc.bzf.busisigning.feign.feign.BbHousingFeignClient;
import com.bonc.ioc.bzf.busisigning.feign.feign.BmsFeignClient;
import com.bonc.ioc.bzf.busisigning.feign.feign.BzfCenterSystemFeignClient;
import lombok.Data;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * feign 配置属性类
 *
 * <AUTHOR>
 * @since 2023/6/2
 */
@Data
@Component
public class FeignConfiguration {

    /**
     * 用户中心 feign实例
     */
    @Resource
    private BzfCenterSystemFeignClient centerSystemFeignClient;
    /**
     * 房态中心 feign实例
     */
    @Resource
    private BbHousingFeignClient housingFeignClient;

    @Resource
    private BmsFeignClient bmsFeignClient;
}
