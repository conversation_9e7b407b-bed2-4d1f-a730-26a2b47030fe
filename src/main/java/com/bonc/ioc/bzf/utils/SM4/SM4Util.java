package com.bonc.ioc.bzf.utils.SM4;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.SM4;
import cn.hutool.crypto.symmetric.SymmetricCrypto;


public class SM4Util {
    public static void main(String[] args) {

        String content = "请对我加密，123!";
        String encrypt = encrypt(content);
        System.out.println(encrypt);
        String decrypt = decrypt(encrypt);
        System.out.println(decrypt);
    }

    /**
     * 和前端key一致
     */
    private static String secretKey = "fc068a17a53492dd";

    /**
     * 和前端iv一致
     */
    private static String iv = "cd068a17a53492dd";

    /**
     * cbc加密
     */
    public static String encrypt(String plainTxt) {
        String cipherTxt = "";
        SymmetricCrypto sm4 = new SM4(
                Mode.CBC, Padding.PKCS5Padding, secretKey.getBytes(CharsetUtil.CHARSET_UTF_8),
                iv.getBytes(CharsetUtil.CHARSET_UTF_8));
        byte[] encrypHex = sm4.encrypt(plainTxt);
        cipherTxt = Base64.encode(encrypHex);
        return cipherTxt;
    }

    /**
     * cbc解密
     */
    public static String decrypt(String cipherTxt) {
        String plainTxt = "";
        try {
            SymmetricCrypto sm4 = new SM4(
                    Mode.CBC, Padding.PKCS5Padding, secretKey.getBytes(CharsetUtil.CHARSET_UTF_8),
                    iv.getBytes(CharsetUtil.CHARSET_UTF_8));
            byte[] cipherHex = Base64.decode(cipherTxt.trim());
            plainTxt = sm4.decryptStr(cipherHex, CharsetUtil.CHARSET_UTF_8);

        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return plainTxt;
    }

}
