package com.bonc.ioc.bzf.busisigning.enums;

/**
 * 退款方式 枚举类
 *
 * <AUTHOR>
 * @since 2024/8/28
 */
public enum RefundMethodEnum {

    /**
     * 补缴
     */
    SUPPLEMENTARY_PAYMENT("1", "补缴"),

    /**
     * 退回银行卡
     */
    RETURN_BANK_CARD("2", "退回银行卡"),

    /**
     * 抵扣租金
     */
    DEDUCTION_RENT("3", "抵扣租金");

    /**
     * 编号
     */
    private String code;

    /**
     * 描述信息
     */
    private String desc;

    /**
     * 获取编号
     *
     * @return 编号
     */
    public String getCode() {
        return this.code;
    }

    /**
     * 获取描述信息
     *
     * @return 描述信息
     */
    public String getDesc() {
        return this.desc;
    }

    /**
     * 构造方法
     *
     * @param code 编号
     * @param desc 描述信息
     */
    RefundMethodEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 构造方法
     */
    RefundMethodEnum() {
    }

    /**
     * 根据编号查询描述信息
     *
     * @param code 编号
     * @return 描述信息
     */
    public static String getDescByCode(String code) {
        RefundMethodEnum[] enums = values();
        for (RefundMethodEnum en : enums) {
            if (en.getCode().equals(code)) {
                return en.getDesc();
            }
        }
        return null;
    }
}
