package com.bonc.ioc.bzf.busisigning.config;

import com.bonc.ioc.bzf.busisigning.feign.feign.BbCustomerFeignClient;
import com.bonc.ioc.bzf.busisigning.feign.feign.BbctContractFeignClient;
import com.bonc.ioc.bzf.busisigning.feign.feign.BbctPaymentV2FeignClient;
import com.bonc.ioc.bzf.busisigning.feign.feign.BbctSigningFeignCilent;
import com.bonc.ioc.bzf.busisigning.feign.feign.BzfSystemCommercialFeignClient;
import com.bonc.ioc.bzf.busisigning.file.feign.BbNewFileFeignClient;
import lombok.Data;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * feign服务 配置属性类
 *
 * <AUTHOR>
 * @since 2024/10/22
 */
@Data
@Component
public class FeignServiceConfiguration {

    /**
     * 签约系统 feign实例
     */
    @Resource
    private BbctSigningFeignCilent signingFeignCilent;

    /**
     * 合同中心 feign实例
     */
    @Resource
    private BbctContractFeignClient contractFeignClient;

    /**
     * 商业内控 feign实例
     */
    @Resource
    private BzfSystemCommercialFeignClient systemCommercialFeignClient;

    /**
     * 文件中心 feign实例
     */
    @Resource
    private BbNewFileFeignClient fileFeignClient;

    /**
     * 客户中心 feign实例
     */
    @Resource
    private BbCustomerFeignClient customerFeignClient;

    /**
     * 缴费中心 feign实例
     */
    @Resource
    private BbctPaymentV2FeignClient paymentV2FeignClient;
}
