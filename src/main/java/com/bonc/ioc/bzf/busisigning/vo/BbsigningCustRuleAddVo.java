package com.bonc.ioc.bzf.busisigning.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 客户签约签约规则配置Vo
 *
 * <AUTHOR>
 * @date 2022-06-07
 * @change 2022-06-07 by wtl for init
 */
@ApiModel(value = "BbsiCustRuleAddVo对象", description = "客户签约规则配置实体Vo")
@Data
@ToString
public class BbsigningCustRuleAddVo extends McpBaseVo implements Serializable {

    private static final long serialVersionUID = 4637825839419155931L;

    /**
     * 计划ID
     */
    @ApiModelProperty(value = "计划ID", required = true)
    @NotBlank(message = "计划ID不能为空", groups = {UpdateValidatorGroup.class})
    private String planId;

    /**
     * 小区ID
     */
    @ApiModelProperty(value = "小区ID", required = true)
    @NotBlank(message = "小区ID不能为空", groups = {UpdateValidatorGroup.class})
    private String communityId;

    /**
     * 规则ID
     */
    @ApiModelProperty(value = "规则ID", required = true)
    @NotBlank(message = "规则ID不能为空", groups = {UpdateValidatorGroup.class})
    private String ruleId;

    /**
     * 全部客户
     */
    @ApiModelProperty(value = "全部客户标识", required = true)
    @NotNull(message = "全部客户标识不能为空", groups = {UpdateValidatorGroup.class})
    private Boolean allCust;

    /**
     * 签约列表
     */
    @ApiModelProperty(value = "签约列表")
    private List<String> signList;
}
