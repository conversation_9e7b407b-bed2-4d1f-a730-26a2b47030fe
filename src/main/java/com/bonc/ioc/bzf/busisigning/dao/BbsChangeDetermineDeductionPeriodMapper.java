package com.bonc.ioc.bzf.busisigning.dao;

import com.bonc.ioc.bzf.busisigning.entity.BbsChangeDetermineDeductionPeriodEntity;
import com.bonc.ioc.common.base.mapper.McpBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.bonc.ioc.bzf.busisigning.vo.*;
import java.util.List;

/**
 * 确定的抵扣账期 Mapper 接口
 *
 * <AUTHOR>
 * @date 2024-10-30
 * @change 2024-10-30 by tbh for init
 */
@Mapper
public interface BbsChangeDetermineDeductionPeriodMapper extends McpBaseMapper<BbsChangeDetermineDeductionPeriodEntity> {
    /**
     * selectByPageCustom 分页查询
     *
     * @param vo 查询条件
     * @return 查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2024-10-30
     * @change 2024-10-30 by tbh for init
     */
    List<BbsChangeDetermineDeductionPeriodPageResultVo> selectByPageCustom(@Param("vo") BbsChangeDetermineDeductionPeriodPageVo vo );


    List<BbsChangeDetermineDeductionPeriodVo> selectByCcId(@Param("ccId") String ccId,@Param("type") String type);

    void deleteByCcId(@Param("ccId") String ccId);
}
