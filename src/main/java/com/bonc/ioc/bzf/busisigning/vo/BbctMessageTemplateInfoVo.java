package com.bonc.ioc.bzf.busisigning.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 消息模板请求参数 vo实体类
 *
 * <AUTHOR>
 * @since 2024/8/22
 */
@Data
public class BbctMessageTemplateInfoVo extends McpBaseVo implements Serializable {

    /**
     * 小区code列表
     */
    @ApiModelProperty(value = "小区code列表")
    private List<String> communityCodes;

    /**
     * 系统类型
     */
    @ApiModelProperty(value = "系统类型")
    private String systemType;

    /**
     * 模板类型
     */
    @ApiModelProperty(value = "模板类型")
    private String templateTypeCode;
}
