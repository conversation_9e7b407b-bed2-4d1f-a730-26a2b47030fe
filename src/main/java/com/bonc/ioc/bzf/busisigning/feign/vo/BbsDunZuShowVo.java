package com.bonc.ioc.bzf.busisigning.feign.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("下列选择趸租合同信息")
public class BbsDunZuShowVo implements Serializable {

    /**
     * 总面积
     */
    @ApiModelProperty("总面积")
    private BigDecimal totalArea;

    /**
     * 小区信息
     */
    @ApiModelProperty("小区信息")
    private List<BbsResidentialQuartersVo> residentialQuartersVoList;
}
