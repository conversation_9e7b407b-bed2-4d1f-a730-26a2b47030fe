package com.bonc.ioc.bzf.busisigning.feign.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value = "BbsSignInfoListVo对象", description = "已经签约的入参实体")
@Data
public class BbsSignedInfoVo implements Serializable {

    @ApiModelProperty(value = "产品类型(01.公租房 07.保租房)")
    private String productType;

    @ApiModelProperty(value = "签约类型(01.散租 02.趸租 03.管理协议)")
    private String signType;

    @ApiModelProperty(value = "签约状态(1.暂存 2.待签约 3.已签约 4.未签约)")
    private String signStatus;

    @ApiModelProperty(value = "散户或者企业编号")
    private String customerNo;
}
