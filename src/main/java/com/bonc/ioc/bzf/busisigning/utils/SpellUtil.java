package com.bonc.ioc.bzf.busisigning.utils;

/**
 * 拼音 工具类
 *
 * <AUTHOR>
 * @since 2024/10/31
 */
public class SpellUtil {

    private static final String[] CN_NUMBER = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九"};
    private static final String[] CN_UNIT = {"", "十", "百", "千", "万"};

    /**
     * 数字转汉字
     *
     * @param number 数字
     * @return 汉字
     */
    public static String numberToChinese(int number) {
        if (number < 0 || number > 99999) {
            throw new IllegalArgumentException("Number should be between 0 and 99999");
        }

        if (number == 0) {
            return CN_NUMBER[0];
        }
        StringBuilder chinese = new StringBuilder();
        int count = 0;
        while (number > 0) {
            int digit = number % 10;
            if (digit != 0) {
                chinese.insert(0, CN_UNIT[count]);
                chinese.insert(0, CN_NUMBER[digit]);
            } else {
                if (chinese.length() > 0 && !chinese.substring(0, 1).equals(CN_NUMBER[0])) {
                    chinese.insert(0, CN_NUMBER[0]);
                }
            }
            number = number / 10;
            count++;
        }
        return chinese.toString();
    }

    /**
     * 构造方法
     */
    private SpellUtil() {
    }
}
