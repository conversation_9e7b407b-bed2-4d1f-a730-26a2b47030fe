package com.bonc.ioc.bzf.busisigning.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @createDate: 2023-09-12 15:06
 * @Version 1.0
 **/
@Data
public class ParamValueList implements Serializable {
    /**
     * 单价
     * */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String PARAMPRICE;
    /**
     * 面积
     * */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String PARAMAREA;
    /**
     * 楼层
     * */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String PARAMFLOOR;
    /**
     * 户型
     * */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String PARAMHOUSETYPE;
    /**
     * 朝向
     * */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String PARAMORIENTATION;
    /**
     * 押金
     * */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String PARAMDEPOSIT;
    /**
     * 总月数
     * */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String PARAMTOTALMONTHS;
}
