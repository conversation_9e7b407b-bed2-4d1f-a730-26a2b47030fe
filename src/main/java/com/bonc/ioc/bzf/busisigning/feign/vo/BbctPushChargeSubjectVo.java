package com.bonc.ioc.bzf.busisigning.feign.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 推送的计费科目信息 vo实体类
 *
 * <AUTHOR>
 * @since 2023/5/25
 */
@Data
@ApiModel(value = "推送的计费科目信息", description = "推送的计费科目信息")
public class BbctPushChargeSubjectVo extends McpBaseVo implements Serializable {

    /**
     * 计费科目编号
     */
    @ApiModelProperty(value = "计费科目编号")
    private String chargeSubjectNo;

    /**
     * 循环计费或单次计费
     */
    @ApiModelProperty(value = "循环计费或单次计费")
    private String cyclicOrSingle;

    /**
     * 计费科目收款周期
     */
    @ApiModelProperty(value = "计费科目收款周期")
    private String chargeSubjectPeriod;

    /**
     * 计费规则编号
     */
    @ApiModelProperty(value = "计费规则编号")
    private String chargeRuleNo;

    /**
     * 计费规则名称
     */
    @ApiModelProperty(value = "计费规则名称")
    private String chargeRuleName;

    /**
     * 参数列表
     */
    @ApiModelProperty(value = "参数列表")
    private BbctChargeRuleSubParamsVo paramList;

    /**
     * 参数值列表
     */
    @ApiModelProperty(value = "参数值列表")
    private BbctChargeRuleSubParamsVo paramValueList;

    /**
     * 计费科目金额
     */
    @ApiModelProperty(value = "计费科目金额")
    private BigDecimal chargeSubjectAmount;

    /**
     * 押金比例
     */
    @ApiModelProperty(value = "押金比例")
    private Integer depositProportion;
}
