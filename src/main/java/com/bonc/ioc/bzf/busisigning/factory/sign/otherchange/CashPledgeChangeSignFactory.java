package com.bonc.ioc.bzf.busisigning.factory.sign.otherchange;

import com.bonc.ioc.bzf.busisigning.config.BusinessServiceConfiguration;
import com.bonc.ioc.bzf.busisigning.config.FeignServiceConfiguration;
import com.bonc.ioc.bzf.busisigning.vo.BbctPreviewInfoParamsVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsSignInfoVo;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * 保证金变更签约 工厂类
 *
 * <AUTHOR>
 * @since 2024/11/4
 */
@Slf4j
public class CashPledgeChangeSignFactory extends AbstractSubChangeSignFactory {

    /**
     * 构造方法
     *
     * @param feignServiceConfiguration    feign服务 配置实例
     * @param businessServiceConfiguration 业务服务 配置实例
     * @param signInfoVo                   签约信息 vo实体
     */
    public CashPledgeChangeSignFactory(FeignServiceConfiguration feignServiceConfiguration,
                                       BusinessServiceConfiguration businessServiceConfiguration,
                                       BbsSignInfoVo signInfoVo) {
        super(feignServiceConfiguration,
                businessServiceConfiguration,
                signInfoVo);
    }

    /**
     * 赋值预览信息map集
     *
     * @param previewInfoMap      预览信息map集
     * @param previewInfoParamsVo 预览信息参数 vo实体
     */
    @Override
    public void setPreviewInfoMap(Map<String, Object> previewInfoMap,
                                  BbctPreviewInfoParamsVo previewInfoParamsVo) {
        return;
    }
}
