package com.bonc.ioc.bzf.busisigning.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 合同变更租金递增设置 实体类
 *
 * <AUTHOR>
 * @date 2024-09-09
 * @change 2024-09-09 by tbh for init
 */
@ApiModel(value="BbsChangeIncrementalConfigVo对象", description="合同变更租金递增设置")
public class BbsChangeIncrementalConfigVo extends McpBaseVo implements Serializable{


    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @NotBlank(message = "主键不能为空",groups = {UpdateValidatorGroup.class})
                                  private String sicId;

    /**
     * 合同变更表主键id
     */
    @ApiModelProperty(value = "合同变更表主键id")
                            private String ccId;

    /**
     * 签约ID
     */
    @ApiModelProperty(value = "签约ID")
                            private String signInfoId;

    /**
     * 标准类型（rent：租金，prop：物业费）
     */
    @ApiModelProperty(value = "标准类型（rent：租金，prop：物业费）")
                            private String standardType;

    /**
     * 调节点，appoint:第，every:每
     */
    @ApiModelProperty(value = "调节点，appoint:第，every:每")
                            private String adjustmentPoint;

    /**
     * 调节点名称
     */
    @ApiModelProperty(value = "调节点名称")
                            private String adjustmentPointName;

    /**
     * 时间点
     */
    @ApiModelProperty(value = "时间点")
                            private Integer timePoint;

    /**
     * 单位，year:年，month:月
     */
    @ApiModelProperty(value = "单位，year:年，month:月")
                            private String unit;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
                            private String unitName;

    /**
     * 涨幅，百分号前数值
     */
    @ApiModelProperty(value = "涨幅，百分号前数值")
                            private Double increase;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
    @NotNull(message = "删除标志;删除标识（1 未删除 0 已删除）不能为空",groups = {UpdateValidatorGroup.class,InsertValidatorGroup.class})
                            private Integer delFlag;

    @ApiModelProperty(value = "类型(01变更前,02变更后)")
    private String type;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    /**
     * @return 主键
     */
    public String getSicId() {
        return sicId;
    }

    public void setSicId(String sicId) {
        this.sicId = sicId;
    }

    /**
     * @return 合同变更表主键id
     */
    public String getCcId() {
        return ccId;
    }

    public void setCcId(String ccId) {
        this.ccId = ccId;
    }

    /**
     * @return 签约ID
     */
    public String getSignInfoId() {
        return signInfoId;
    }

    public void setSignInfoId(String signInfoId) {
        this.signInfoId = signInfoId;
    }

    /**
     * @return 标准类型（rent：租金，prop：物业费）
     */
    public String getStandardType() {
        return standardType;
    }

    public void setStandardType(String standardType) {
        this.standardType = standardType;
    }

    /**
     * @return 调节点，appoint:第，every:每
     */
    public String getAdjustmentPoint() {
        return adjustmentPoint;
    }

    public void setAdjustmentPoint(String adjustmentPoint) {
        this.adjustmentPoint = adjustmentPoint;
    }

    /**
     * @return 调节点名称
     */
    public String getAdjustmentPointName() {
        return adjustmentPointName;
    }

    public void setAdjustmentPointName(String adjustmentPointName) {
        this.adjustmentPointName = adjustmentPointName;
    }

    /**
     * @return 时间点
     */
    public Integer getTimePoint() {
        return timePoint;
    }

    public void setTimePoint(Integer timePoint) {
        this.timePoint = timePoint;
    }

    /**
     * @return 单位，year:年，month:月
     */
    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    /**
     * @return 单位名称
     */
    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    /**
     * @return 涨幅，百分号前数值
     */
    public Double getIncrease() {
        return increase;
    }

    public void setIncrease(Double increase) {
        this.increase = increase;
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

      @Override
    public String toString() {
        return "BbsChangeIncrementalConfigVo{" +
            "sicId=" + sicId +
            ", ccId=" + ccId +
            ", signInfoId=" + signInfoId +
            ", standardType=" + standardType +
            ", adjustmentPoint=" + adjustmentPoint +
            ", adjustmentPointName=" + adjustmentPointName +
            ", timePoint=" + timePoint +
            ", unit=" + unit +
            ", unitName=" + unitName +
            ", increase=" + increase +
            ", delFlag=" + delFlag +
        "}";
    }
}
