package com.bonc.ioc.bzf.busisigning.vo;

import com.bonc.ioc.common.base.vo.McpBasePageVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 客户签约列表查询请求实体
 *
 * <AUTHOR>
 * @date 2022-06-07
 * @change 2022-06-07 by wtl for init
 */
@ApiModel(value = "客户签约列表查询请求实体", description = "客户签约列表查询请求实体")
@Data
public class CustSignListRequestVo extends McpBasePageVo implements Serializable {

    private static final long serialVersionUID = -2418759147114553701L;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String signState;

    /**
     * 客户姓名
     */
    @ApiModelProperty(value = "客户姓名")
    private String custName;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private Date beginTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private Date endTime;

}
