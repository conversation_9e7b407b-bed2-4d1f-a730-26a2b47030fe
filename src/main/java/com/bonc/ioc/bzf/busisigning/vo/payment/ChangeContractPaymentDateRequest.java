package com.bonc.ioc.bzf.busisigning.vo.payment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 合同变更 商业账单缴费日变更
 */
@Data
public class ChangeContractPaymentDateRequest implements Serializable {
    @ApiModelProperty(value = "变更类型")
    private String changeType;
    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "商业账单缴费日变更")
    private BusinessContractChangePaymentDateDTO businessContractChangePaymentDateDTO;//businessContractChangePaymentDateDTO(商业账单缴费日变更)


}
