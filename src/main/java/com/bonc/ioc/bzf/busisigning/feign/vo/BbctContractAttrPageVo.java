package com.bonc.ioc.bzf.busisigning.feign.vo;

import com.bonc.ioc.common.base.vo.McpBasePageVo;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 合同属性 实体类
 *
 * <AUTHOR>
 * @date 2023-05-04
 * @change 2023-05-04 by fem for init
 */
@ApiModel(value="BbctContractAttrPageVo对象", description="合同属性")
public class BbctContractAttrPageVo extends McpBasePageVo implements Serializable{


    /**
     * 合同属性ID
     */
    @ApiModelProperty(value = "合同属性ID")
    @NotBlank(message = "合同属性ID不能为空",groups = {UpdateValidatorGroup.class})
                                  private String contractAtrrId;

    /**
     * 所属集合名称
     */
    @ApiModelProperty(value = "所属集合名称")
                            private String parentSeatKey;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
                            private String contractNo;

    /**
     * 合同ID
     */
    @ApiModelProperty(value = "合同ID")
                            private String contractId;

    /**
     * 合同模板id
     */
    @ApiModelProperty(value = "合同模板id")
                            private String contractTemplateId;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
                            private String name;

    /**
     * 占位符KEY
     */
    @ApiModelProperty(value = "占位符KEY")
                            private String seatKey;

    /**
     * 值
     */
    @ApiModelProperty(value = "值")
                            private String val;

    /**
     * seat_type
     */
    @ApiModelProperty(value = "seat_type")
                            private String seatType;

    /**
     * 信息组(当这个占位符是列表中的一个属性时，每个对象的属性是一串相同的uuid，以此确定列表中每一行信息)
     */
    @ApiModelProperty(value = "信息组(当这个占位符是列表中的一个属性时，每个对象的属性是一串相同的uuid，以此确定列表中每一行信息)")
                            private String fieldGroup;

    /**
     * 删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标识（1 未删除 0 已删除）")
                            private Integer delFlag;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
                            private String remark;

    /**
     * 对应合同字段
     */
    @ApiModelProperty(value = "对应合同字段")
                            private String fieldName;

    /**
     * @return 合同属性ID
     */
    public String getContractAtrrId() {
        return contractAtrrId;
    }

    public void setContractAtrrId(String contractAtrrId) {
        this.contractAtrrId = contractAtrrId;
    }

    /**
     * @return 所属集合名称
     */
    public String getParentSeatKey() {
        return parentSeatKey;
    }

    public void setParentSeatKey(String parentSeatKey) {
        this.parentSeatKey = parentSeatKey;
    }

    /**
     * @return 合同编号
     */
    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    /**
     * @return 合同ID
     */
    public String getContractId() {
        return contractId;
    }

    public void setContractId(String contractId) {
        this.contractId = contractId;
    }

    /**
     * @return 合同模板id
     */
    public String getContractTemplateId() {
        return contractTemplateId;
    }

    public void setContractTemplateId(String contractTemplateId) {
        this.contractTemplateId = contractTemplateId;
    }

    /**
     * @return 标题
     */
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    /**
     * @return 占位符KEY
     */
    public String getSeatKey() {
        return seatKey;
    }

    public void setSeatKey(String seatKey) {
        this.seatKey = seatKey;
    }

    /**
     * @return 值
     */
    public String getVal() {
        return val;
    }

    public void setVal(String val) {
        this.val = val;
    }

    /**
     * @return seat_type
     */
    public String getSeatType() {
        return seatType;
    }

    public void setSeatType(String seatType) {
        this.seatType = seatType;
    }

    /**
     * @return 信息组(当这个占位符是列表中的一个属性时，每个对象的属性是一串相同的uuid，以此确定列表中每一行信息)
     */
    public String getFieldGroup() {
        return fieldGroup;
    }

    public void setFieldGroup(String fieldGroup) {
        this.fieldGroup = fieldGroup;
    }

    /**
     * @return 删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * @return 描述
     */
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * @return 对应合同字段
     */
    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

      @Override
    public String toString() {
        return "BbctContractAttrPageVo{" +
            "contractAtrrId=" + contractAtrrId +
            ", parentSeatKey=" + parentSeatKey +
            ", contractNo=" + contractNo +
            ", contractId=" + contractId +
            ", contractTemplateId=" + contractTemplateId +
            ", name=" + name +
            ", seatKey=" + seatKey +
            ", val=" + val +
            ", seatType=" + seatType +
            ", fieldGroup=" + fieldGroup +
            ", delFlag=" + delFlag +
            ", remark=" + remark +
            ", fieldName=" + fieldName +
        "}";
    }
}
