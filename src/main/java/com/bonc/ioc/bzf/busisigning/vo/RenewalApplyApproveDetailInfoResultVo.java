package com.bonc.ioc.bzf.busisigning.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 续签申请信息分页列表 - 查询操作记录返回的数据
 *
 * @author: he<PERSON>ya<PERSON>
 * @createDate: 2023-09-21
 * @Version 1.0
 **/
@Data
@ApiModel(value = "RenewalApplyApproveDetailInfoResultVo", description = "续签申请信息分页列表")
public class RenewalApplyApproveDetailInfoResultVo {

    /**
     * 审批明细id
     */
    @ApiModelProperty(value = "审批明细id")
    private String approveDetailId;

    /**
     * 审批id
     */
    @ApiModelProperty(value = "审批id")
    private String approveId;

    /**
     * 上级id（合同编码）
     */
    @ApiModelProperty(value = "上级id（合同编码）")
    private String parentId;

    /**
     * 意见说明
     */
    @ApiModelProperty(value = "意见说明")
    private String commentExplanation;

    /**
     * 审批状态(1.通过 2.未通过 3.待审核 4.撤回)
     */
    @ApiModelProperty(value = "审批状态(1.通过 2.未通过 3.待审核 4.撤回)")
    private String approveStatus;

    /**
     * 审批时间
     */
    @ApiModelProperty(value = "审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approveTime;

    /**
     * 提交人id
     */
    @ApiModelProperty(value = "提交人id")
    private String submitUserId;

    /**
     * 提交人姓名
     */
    @ApiModelProperty(value = "提交人姓名")
    private String submitUserName;

    /**
     * 提交时间
     */
    @ApiModelProperty(value = "提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date submitTime;

    /**
     * 审批人id
     */
    @ApiModelProperty(value = "审批人id")
    private String approverUserId;

//    /**
//     * 删除标识(1.未删除 0.已删除)
//     */
//    @ApiModelProperty(value = "删除标识(1.未删除 0.已删除)")
//    private Integer delFlag;

    /**
     * 审批人姓名
     */
    @ApiModelProperty(value = "审批人姓名")
    private String approverUserName;
    /**
     * 审批描述
     */
    @ApiModelProperty(value = "审批描述")
    private String approvDesc;

}


