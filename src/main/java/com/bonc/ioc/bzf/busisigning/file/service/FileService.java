package com.bonc.ioc.bzf.busisigning.file.service;


import com.bonc.ioc.bzf.busisigning.file.vo.FIleResultVo;
import com.bonc.ioc.bzf.busisigning.file.vo.NewFIleResultVo;
import com.bonc.ioc.common.util.AppReply;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @ClassName FileService
 * @Description TODO
 * @AUTHOR 宋鑫
 * @Date 2023-02-28 12:17
 **/
public interface FileService {
    /**
     * @description: 文件下载
     * @author: 宋鑫
     * @date: 2023-02-28 12:19
     * @param: [fileId, response]
     * @return: void
     * @since 1.0.0
     **/

    void downloadFileRecord(Integer fileId, HttpServletResponse response);

    /**
     * 将新接口返回数据重新封装成旧接口返回数据
     * @param fResultVo
     * @return
     */
    AppReply<FIleResultVo> toOldFileResultVo(AppReply<List<NewFIleResultVo>> fResultVo);


    /**
     * @description: 上传文件
     * @author: 宋鑫
     * @date: 2023-03-10 10:47
     * @param: [file, project, folderName]
     * @return: com.bonc.ioc.common.util.AppReply<java.lang.Integer>
     * @since 1.0.0
     **/
    AppReply<String> uploadFile(MultipartFile file,String project,String folderName);



}
