package com.bonc.ioc.bzf.busisigning.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bonc.ioc.common.base.entity.McpBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 签约-免租期-区间（当租金/物业费免租期分类为1时有效） 实体类
 *
 * <AUTHOR>
 * @date 2023-09-01
 * @change 2023-09-01 by l<PERSON><PERSON><PERSON><PERSON> for init
 */
@TableName("bbs_sign_fp_interval")
@ApiModel(value = "BbsSignFpIntervalEntity对象", description = "签约-免租期-区间（当租金/物业费免租期分类为1时有效）")
public class BbsSignFpIntervalEntity extends McpBaseEntity implements Serializable {

    public static final String FIELD_SFI_ID = "sfi_id";
    public static final String FIELD_SIGN_INFO_ID = "sign_info_id";
    public static final String FIELD_STANDARD_TYPE = "standard_type";
    public static final String FIELD_START = "start";
    public static final String FIELD_END = "end";
    public static final String FIELD_DEL_FLAG = "del_flag";

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @TableId(value = "sfi_id", type = IdType.ASSIGN_UUID)
    private String sfiId;

    /**
     * 签约结果ID
     */
    @ApiModelProperty(value = "签约结果ID")
    private String signInfoId;

    /**
     * 标准类型（rent：租金，prop：物业费）
     */
    @ApiModelProperty(value = "标准类型（rent：租金，prop：物业费）")
    private String standardType;

    /**
     * 开始值
     */
    @ApiModelProperty(value = "开始值")
    private String start;

    /**
     * 结束值
     */
    @ApiModelProperty(value = "结束值")
    private String end;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
    private Integer delFlag;

    /**
     * @return 主键
     */
    public String getSfiId() {
        return sfiId;
    }

    public void setSfiId(String sfiId) {
        this.sfiId = sfiId;
    }

    /**
     * @return 签约结果ID
     */
    public String getSignInfoId() {
        return signInfoId;
    }

    public void setSignInfoId(String signInfoId) {
        this.signInfoId = signInfoId;
    }

    /**
     * @return 标准类型（rent：租金，prop：物业费）
     */
    public String getStandardType() {
        return standardType;
    }

    public void setStandardType(String standardType) {
        this.standardType = standardType;
    }

    /**
     * @return 开始值
     */
    public String getStart() {
        return start;
    }

    public void setStart(String start) {
        this.start = start;
    }

    /**
     * @return 结束值
     */
    public String getEnd() {
        return end;
    }

    public void setEnd(String end) {
        this.end = end;
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    @Override
    public String toString() {
        return "BbsSignFpIntervalEntity{" +
                "sfiId=" + sfiId +
                ", signInfoId=" + signInfoId +
                ", standardType=" + standardType +
                ", start=" + start +
                ", end=" + end +
                ", delFlag=" + delFlag +
                "}";
    }
}