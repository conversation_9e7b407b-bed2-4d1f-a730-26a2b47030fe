package com.bonc.ioc.bzf.busisigning.feign.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 推送报文 vo实体类
 *
 * <AUTHOR>
 * @since 2023/5/25
 */
@Data
@ApiModel(value = "推送报文", description = "推送报文")
public class BbctPushInfoVo extends McpBaseVo implements Serializable {

    /**
     * 产品信息列表
     */
    @ApiModelProperty(value = "产品信息列表")
    private List<BbctPushProductInfoVo> productInfoList;

    /**
     * 签约方信息列表
     */
    @ApiModelProperty(value = "签约方信息列表")
    private List<BbctPushSignatoryInfoVo> signatoryInfoList;

    /**
     * 合同来源类型
     */
    @ApiModelProperty(value = "合同来源类型")
    private String contractSourceType;

    /**
     * 上级合同编号
     */
    @ApiModelProperty(value = "上级合同编号")
    private String parentContractCode;

    /**
     * 产品类型
     */
    @ApiModelProperty(value = "产品类型")
    private String productType;

    /**
     * 合同类型
     */
    @ApiModelProperty(value = "合同类型")
    private String contractType;

    /**
     * 是否历史合同数据
     */
    @ApiModelProperty(value = "是否历史合同数据")
    private String historyData;

    /**
     * 新合同ID
     */
    @ApiModelProperty(value = "新合同ID")
    private String contractId;

    /**
     * NC57老合同ID
     */
    @ApiModelProperty(value = "NC57老合同ID")
    private String oldContractId;

    /**
     * 合同分类
     */
    @ApiModelProperty(value = "合同分类")
    private String contractClassification;

    /**
     * 合同当前状态
     */
    @ApiModelProperty(value = "合同当前状态")
    private String contractStatus;

    /**
     * 合同起始日期
     */
    @ApiModelProperty(value = "合同起始日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date contractBeginDate;

    /**
     * 合同终止日期
     */
    @ApiModelProperty(value = "合同终止日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date contractEndDate;

    /**
     * 合同签订时间
     */
    @ApiModelProperty(value = "合同签订时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date contractSignTime;

    /**
     * 合同起租日
     */
    @ApiModelProperty(value = "合同起租日")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date contractCommencementDate;

    /**
     * 合同签约计价单位
     */
    @ApiModelProperty(value = "合同签约计价单位")
    private String contractPriceUnit;

    /**
     * 合同计价周期
     */
    @ApiModelProperty(value = "合同计价周期")
    private String contractPricePeriod;

    /**
     * 合同面积
     */
    @ApiModelProperty(value = "合同面积")
    private BigDecimal contractArea;

    /**
     * 审批人
     */
    @ApiModelProperty(value = "审批人")
    private String approver;

    /**
     * 审批时间
     */
    @ApiModelProperty(value = "审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approveTime;

    /**
     * 是否存在关联合同
     */
    @ApiModelProperty(value = "是否存在关联合同")
    private String relationContract;

    /**
     * 关联合同编号
     */
    @ApiModelProperty(value = "关联合同编号")
    private String relationContractCode;

    /**
     * 分摊方式
     */
    @ApiModelProperty(value = "分摊方式")
    private String shareType;

    /**
     * 资格通过时间
     */
    @ApiModelProperty(value = "资格通过时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date qualificationPassDate;
}
