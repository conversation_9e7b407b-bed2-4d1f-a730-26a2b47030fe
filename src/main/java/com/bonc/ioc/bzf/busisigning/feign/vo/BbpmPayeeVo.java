package com.bonc.ioc.bzf.busisigning.feign.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 收款回传数据
 *
 * <AUTHOR>
 * @date 2023/1/3 14:22
 * @change 2023/1/3 14:22 by <PERSON><PERSON><PERSON><PERSON> for init
 */
@Data
public class BbpmPayeeVo implements Serializable {
    @ApiModelProperty(value = "主键id")
    private String pospbakId;

    @ApiModelProperty(value = "收款回传请求json")
    private String request;

    @ApiModelProperty(value = "收款回传响应json")
    private String result;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
    private Integer delFlag;

}
