package com.bonc.ioc.bzf.busisigning.vo;

import com.bonc.ioc.bzf.busisigning.file.vo.FIleResultVo;
import lombok.Data;

import java.util.List;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @createDate: 2023-09-04 11:15
 * @Version 1.0
 **/
@Data
public class ViewTransactorsVo {
    /**
     * 委托人代办人姓名
     * */
    private String consignorName;
    /**
     * 与主承租人关系(1.朋友 2.亲属)
     */
    private String consignorSignatoryRelationType;
    /**
     * 委托人证件类型(1.身份证 2.户口本)
     * */
    private String consignorCertificationType;
    /**
     * 委托人证件号码
     * */
    private String consignorIdentityCard;
    /**
     * 委托人手机号码
     * */
    private String consignorMobile;
    /**
     * 委托人上传委托材料
     * */
    private List<FIleResultVo> consignorDataUrls;
    /**
     * 委托人身份证正反面照片
     */
    private List<FIleResultVo> consignorIdentityCardPhotoUrl;
    /**
     * 本人办理 姓名
     * */
    private String customerName;
    /**
     * 本人办理 证件类型
     * */
    private String customerIdType;
    /**
     * 本人办理 证件号码
     * */
    private String customerIdNumber;
    /**
     * 办理人照片地址
     * */
    private List<FIleResultVo> transactorIdentityCardPhotoUrl;

    /**
     * 签约办理人类型(1.本人办理 2.委托人办理)
     */
    private String transactorType;
}
