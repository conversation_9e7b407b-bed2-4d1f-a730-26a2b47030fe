package com.bonc.ioc.bzf.busisigning.service.impl;

import com.bonc.ioc.bzf.busisigning.dao.BbsImportRecordMapper;
import com.bonc.ioc.bzf.busisigning.entity.BbsImportRecordEntity;
import com.bonc.ioc.bzf.busisigning.service.IBbsImportRecordService;
import com.bonc.ioc.bzf.busisigning.vo.BbsImportRecordPageResultVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsImportRecordPageVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsImportRecordVo;
import com.bonc.ioc.common.base.page.PageResult;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import com.bonc.ioc.common.exception.McpException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 导入记录表 服务类实现
 *
 * <AUTHOR>
 * @date 2024-03-07
 * @change 2024-03-07 by pyj for init
 */
@Slf4j
@Service
public class BbsImportRecordServiceImpl extends McpBaseServiceImpl<BbsImportRecordEntity> implements IBbsImportRecordService {

    /**
     * mapper 访问数据库
     */
    @Resource
    private BbsImportRecordMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbsImportRecordService baseService;

    /**
     * insertRecord 新增
     *
     * @param vo 需要保存的记录
     * @return String 返回新增后的主键
     * <AUTHOR>
     * @date 2024-03-07
     * @change 2024-03-07 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public String insertRecord(BbsImportRecordVo vo) {
        if (vo == null) {
            return null;
        }

        BbsImportRecordEntity entity = new BbsImportRecordEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setImportId(null);
        if (!baseService.insert(entity)) {
            log.error("导入记录表新增失败:" + entity.toString());
            throw new McpException("导入记录表新增失败");
        } else {
            if (!baseService.saveOperationHisById(entity.getImportId(), 1)) {
                log.error("导入记录表新增后保存历史失败:" + entity.toString());
                throw new McpException("导入记录表新增后保存历史失败");
            }

            log.debug("导入记录表新增成功:" + entity.getImportId());
            return entity.getImportId();
        }
    }

    /**
     * insertBatchRecord 批量新增
     *
     * @param voList 需要保存的记录
     * @return List<String> 返回新增后的主键
     * <AUTHOR>
     * @date 2024-03-07
     * @change 2024-03-07 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbsImportRecordVo> voList) {
        if (CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbsImportRecordEntity> entityList = new ArrayList<>();
        for (BbsImportRecordVo item : voList) {
            BbsImportRecordEntity entity = new BbsImportRecordEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbsImportRecordEntity item : entityList) {
            item.setImportId(null);
        }

        if (!baseService.insertBatch(entityList)) {
            log.error("导入记录表新增失败");
            throw new McpException("导入记录表新增失败");
        } else {
            List<String> kidList = entityList.stream().map(BbsImportRecordEntity::getImportId).collect(Collectors.toList());

            if (!baseService.saveOperationHisByIds(kidList, 1)) {
                log.error("导入记录表批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("导入记录表批量新增后保存历史失败");
            }

            log.debug("导入记录表新增成功:" + StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * 批量插入
     *
     * @param voList 对象列表
     * @return 插入结果
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertObjectBatchRecord(List<Object> voList) {
        if (CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbsImportRecordEntity> entityList = new ArrayList<>();
        for (Object item : voList) {
            BbsImportRecordEntity entity = new BbsImportRecordEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbsImportRecordEntity item : entityList) {
            item.setImportId(null);
        }

        if (!baseService.insertBatch(entityList)) {
            log.error("导入记录表新增失败");
            throw new McpException("导入记录表新增失败");
        } else {
            List<String> kidList = entityList.stream().map(BbsImportRecordEntity::getImportId).collect(Collectors.toList());

            if (!baseService.saveOperationHisByIds(kidList, 1)) {
                log.error("导入记录表批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("导入记录表批量新增后保存历史失败");
            }

            log.debug("导入记录表新增成功:" + StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     *
     * @param importId 需要删除的主键id
     * @return void
     * <AUTHOR>
     * @date 2024-03-07
     * @change 2024-03-07 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String importId) {
        if (!StringUtils.isEmpty(importId)) {
            if (!baseService.saveOperationHisById(importId, 3)) {
                log.error("导入记录表删除后保存历史失败:" + importId);
                throw new McpException("导入记录表删除后保存历史失败");
            }

            if (!baseService.removeById(importId)) {
                log.error("导入记录表删除失败");
                throw new McpException("导入记录表删除失败" + importId);
            }
        } else {
            throw new McpException("导入记录表删除失败主键id为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     *
     * @param importIdList 需要删除的主键id
     * @return void
     * <AUTHOR>
     * @date 2024-03-07
     * @change 2024-03-07 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> importIdList) {
        if (!CollectionUtils.isEmpty(importIdList)) {
            int oldSize = importIdList.size();
            importIdList = importIdList.stream().filter(t -> StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(importIdList) || oldSize != importIdList.size()) {
                throw new McpException("导入记录表批量删除失败 存在主键id为空的记录" + StringUtils.join(importIdList));
            }

            if (!baseService.saveOperationHisByIds(importIdList, 3)) {
                log.error("导入记录表批量删除后保存历史失败:" + StringUtils.join(importIdList));
                throw new McpException("导入记录表批量删除后保存历史失败");
            }

            if (!baseService.removeByIds(importIdList)) {
                log.error("导入记录表批量删除失败");
                throw new McpException("导入记录表批量删除失败" + StringUtils.join(importIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     *
     * @param vo 需要更新的导入记录表
     * @return void
     * <AUTHOR>
     * @date 2024-03-07
     * @change 2024-03-07 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbsImportRecordVo vo) {
        if (vo != null) {
            BbsImportRecordEntity entity = new BbsImportRecordEntity();
            BeanUtils.copyProperties(vo, entity);

            if (StringUtils.isEmpty(entity.getImportId())) {
                throw new McpException("导入记录表更新失败传入主键id为空");
            }

            if (!baseService.updateById(entity)) {
                log.error("导入记录表更新失败");
                throw new McpException("导入记录表更新失败" + entity.getImportId());
            } else {
                if (!baseService.saveOperationHisById(entity.getImportId(), 2)) {
                    log.error("导入记录表更新后保存历史失败:" + entity.getImportId());
                    throw new McpException("导入记录表更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("导入记录表更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     *
     * @param voList 需要更新的导入记录表
     * @return void
     * <AUTHOR>
     * @date 2024-03-07
     * @change 2024-03-07 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbsImportRecordVo> voList) {
        if (!CollectionUtils.isEmpty(voList)) {
            List<BbsImportRecordEntity> entityList = new ArrayList<>();

            for (BbsImportRecordVo item : voList) {
                BbsImportRecordEntity entity = new BbsImportRecordEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t -> StringUtils.isNotBlank(t.getImportId())).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("导入记录表批量更新失败 存在主键id为空的记录");
            }

            if (!baseService.updateBatchById(entityList)) {
                log.error("导入记录表批量更新失败");
                throw new McpException("导入记录表批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t -> StringUtils.isNotBlank(t.getImportId())).map(BbsImportRecordEntity::getImportId).collect(Collectors.toList());
                if (!baseService.saveOperationHisByIds(kidList, 2)) {
                    log.error("导入记录表批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("导入记录表批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     *
     * @param vo 需要更新的导入记录表
     * @return void
     * <AUTHOR>
     * @date 2024-03-07
     * @change 2024-03-07 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbsImportRecordVo vo) {
        if (vo != null) {
            BbsImportRecordEntity entity = new BbsImportRecordEntity();
            BeanUtils.copyProperties(vo, entity);

            if (!baseService.saveById(entity)) {
                log.error("导入记录表保存失败");
                throw new McpException("导入记录表保存失败" + entity.getImportId());
            } else {
                if (!baseService.saveOperationHisById(entity.getImportId(), 4)) {
                    log.error("导入记录表保存后保存历史失败:" + entity.getImportId());
                    throw new McpException("导入记录表保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("导入记录表保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     *
     * @param voList 需要删除的导入记录表
     * @return void
     * <AUTHOR>
     * @date 2024-03-07
     * @change 2024-03-07 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbsImportRecordVo> voList) {
        if (!CollectionUtils.isEmpty(voList)) {
            List<BbsImportRecordEntity> entityList = new ArrayList<>();

            for (BbsImportRecordVo item : voList) {
                BbsImportRecordEntity entity = new BbsImportRecordEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if (!baseService.saveBatchById(entityList)) {
                log.error("导入记录表批量保存失败");
                throw new McpException("导入记录表批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t -> StringUtils.isNotBlank(t.getImportId())).map(BbsImportRecordEntity::getImportId).collect(Collectors.toList());

                if (!baseService.saveOperationHisByIds(kidList, 4)) {
                    log.error("导入记录表批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("导入记录表批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     *
     * @param importId 需要查询的主键id
     * @return 根据id查询结果
     * <AUTHOR>
     * @date 2024-03-07
     * @change 2024-03-07 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbsImportRecordVo selectByIdRecord(String importId) {
        BbsImportRecordVo vo = new BbsImportRecordVo();

        if (!StringUtils.isEmpty(importId)) {
            BbsImportRecordEntity entity = baseService.selectById(importId);

            if (entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     *
     * @param vo 需要查询的条件
     * @return 分页查询结果
     * <AUTHOR>
     * @date 2024-03-07
     * @change 2024-03-07 by pyj for init
     * @since 1.0.0
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbsImportRecordPageResultVo>> selectByPageRecord(BbsImportRecordPageVo vo) {
        List<BbsImportRecordPageResultVo> result = baseMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }
}
