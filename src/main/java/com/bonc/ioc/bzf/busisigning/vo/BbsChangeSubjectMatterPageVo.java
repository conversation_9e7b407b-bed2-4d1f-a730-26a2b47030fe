package com.bonc.ioc.bzf.busisigning.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.vo.McpBasePageVo;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 缩租面积变更历史产品信息表 实体类
 *
 * <AUTHOR>
 * @date 2024-10-25
 * @change 2024-10-25 by tbh for init
 */
@ApiModel(value="BbsChangeSubjectMatterPageVo对象", description="缩租面积变更历史产品信息表")
public class BbsChangeSubjectMatterPageVo extends McpBasePageVo implements Serializable{


    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @NotBlank(message = "主键不能为空",groups = {UpdateValidatorGroup.class})
                                  private String subjectMatterId;

    /**
     * 合同变更表主键id
     */
    @ApiModelProperty(value = "合同变更表主键id")
                            private String ccId;

    /**
     * 面积类型(1.建筑面积 2.套内建筑面积)
     */
    @ApiModelProperty(value = "面积类型(1.建筑面积 2.套内建筑面积)")
                            private String areaType;

    /**
     * 关系表ID
     */
    @ApiModelProperty(value = "关系表ID")
                            private String relationId;

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
                            private String contractId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
                            private String contractNo;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
                            private String productName;

    /**
     * 产品地址
     */
    @ApiModelProperty(value = "产品地址")
                            private String productAddress;

    /**
     * 产品编号
     */
    @ApiModelProperty(value = "产品编号")
                            private String productNo;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
                            private String projectId;

    /**
     * 项目编号（业务上没有使用）
     */
    @ApiModelProperty(value = "项目编号（业务上没有使用）")
                            private String projectNo;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
                            private String projectName;

    /**
     * 是否空置（0否，1是）
     */
    @ApiModelProperty(value = "是否空置（0否，1是）")
                            private String vacantStatus;

    /**
     * 保障房-租金标准类型(01.公租租金标准 02.人才租金标准 03.市场租金标准 04.一口价)
     */
    @ApiModelProperty(value = "保障房-租金标准类型(01.公租租金标准 02.人才租金标准 03.市场租金标准 04.一口价)")
                            private String rentStandardNo;

    /**
     * 保障房-租金标准类型
     */
    @ApiModelProperty(value = "保障房-租金标准类型")
                            private String rentStandardName;

    /**
     * 租金标准（元/m²/月）金额
     */
    @ApiModelProperty(value = "租金标准（元/m²/月）金额")
                            private Double rent;

    /**
     * 租金
     */
    @ApiModelProperty(value = "租金")
                            private Double leaseTermRent;

    /**
     * 租期
     */
    @ApiModelProperty(value = "租期")
                            private String leaseTerm;

    /**
     * 保证金
     */
    @ApiModelProperty(value = "保证金")
                            private Double deposit;

    /**
     * 选房渠道
     */
    @ApiModelProperty(value = "选房渠道")
                            private String houseSelectionChannelsNo;

    /**
     * 选房渠道名称
     */
    @ApiModelProperty(value = "选房渠道名称")
                            private String houseSelectionChannelsName;

    /**
     * 小区或楼宇
     */
    @ApiModelProperty(value = "小区或楼宇")
                            private String communityBuildingNo;

    /**
     * 小区或楼宇名称
     */
    @ApiModelProperty(value = "小区或楼宇名称")
                            private String communityBuildingName;

    /**
     * 组团
     */
    @ApiModelProperty(value = "组团")
                            private String groupNo;

    /**
     * 组团名称
     */
    @ApiModelProperty(value = "组团名称")
                            private String groupName;

    /**
     * 楼宇编号
     */
    @ApiModelProperty(value = "楼宇编号")
                            private String buildingNo;

    /**
     * 楼宇名称
     */
    @ApiModelProperty(value = "楼宇名称")
                            private String buildingName;

    /**
     * 单元号
     */
    @ApiModelProperty(value = "单元号")
                            private String unitNo;

    /**
     * 单元名称
     */
    @ApiModelProperty(value = "单元名称")
                            private String unitName;

    /**
     * 户型类别编码
     */
    @ApiModelProperty(value = "户型类别编码")
                            private String houseTypeNo;

    /**
     * 户型类别
     */
    @ApiModelProperty(value = "户型类别")
                            private String houseTypeName;

    /**
     * 建筑面积
     */
    @ApiModelProperty(value = "建筑面积")
                            private String houseStructArea;

    /**
     * 套内建筑面积
     */
    @ApiModelProperty(value = "套内建筑面积")
                            private String innerSleeveArea;

    /**
     * 房屋使用面积
     */
    @ApiModelProperty(value = "房屋使用面积")
                            private String useArea;

    /**
     * 套型
     */
    @ApiModelProperty(value = "套型")
                            private String jacketed;

    /**
     * 套型编号
     */
    @ApiModelProperty(value = "套型编号")
                            private String jacketedCode;

    /**
     * 房间号
     */
    @ApiModelProperty(value = "房间号")
                            private String roomNo;

    /**
     * 房间名称
     */
    @ApiModelProperty(value = "房间名称")
                            private String roomName;

    /**
     * 所在区县
     */
    @ApiModelProperty(value = "所在区县")
                            private String communityRegion;

    /**
     * 小区地址
     */
    @ApiModelProperty(value = "小区地址")
                            private String communityAddress;

    /**
     * 产品扩展
     */
    @ApiModelProperty(value = "产品扩展")
                            private String productExtend;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
                            private Integer delFlag;

    /**
     * 保障房-标的业务分类1
     */
    @ApiModelProperty(value = "保障房-标的业务分类1")
                            private String productBussinessType1;

    /**
     * 保障房-标的业务分类2
     */
    @ApiModelProperty(value = "保障房-标的业务分类2")
                            private String productBussinessType2;

    /**
     * 保障房-标的业务分类3
     */
    @ApiModelProperty(value = "保障房-标的业务分类3")
                            private String productBussinessType3;

    /**
     * 所在层次
     */
    @ApiModelProperty(value = "所在层次")
                            private String currentFloorNo;

    /**
     * 总层数
     */
    @ApiModelProperty(value = "总层数")
                            private String totalFloorNo;

    /**
     * 房屋朝向
     */
    @ApiModelProperty(value = "房屋朝向")
                            private String houseOrientation;

    /**
     * 项目简称
     */
    @ApiModelProperty(value = "项目简称")
                            private String projectShortName;

    /**
     * 运营主体类型
     */
    @ApiModelProperty(value = "运营主体类型")
                            private String operateEntityType;

    /**
     * 运营单位
     */
    @ApiModelProperty(value = "运营单位")
                            private String operateEntityName;

    /**
     * 运营单位业务中台编号
     */
    @ApiModelProperty(value = "运营单位业务中台编号")
                            private String operateUnitBusinessNo;

    /**
     * 运营单位编号（NCC）
     */
    @ApiModelProperty(value = "运营单位编号（NCC）")
                            private String operateUnitNo;

    /**
     * 运营单位名称
     */
    @ApiModelProperty(value = "运营单位名称")
                            private String operateUnitName;

    /**
     * 项目区域业务中台编号
     */
    @ApiModelProperty(value = "项目区域业务中台编号")
                            private String projectAreaBusinessNo;

    /**
     * 项目区域编号（NCC）
     */
    @ApiModelProperty(value = "项目区域编号（NCC）")
                            private String projectAreaNo;

    /**
     * 项目区域名称
     */
    @ApiModelProperty(value = "项目区域名称")
                            private String projectAreaName;

    /**
     * 项目业态（01 公租房 02 保租房 03 商业 04 共有产权房 05 仓储 06车位）
     */
    @ApiModelProperty(value = "项目业态（01 公租房 02 保租房 03 商业 04 共有产权房 05 仓储 06车位）")
                            private String projectFormat;

    /**
     * 所在小区或楼宇名称
     */
    @ApiModelProperty(value = "所在小区或楼宇名称")
                            private String projectEstate;

    /**
     * 居室名称
     */
    @ApiModelProperty(value = "居室名称")
                            private String bedRoom;

    /**
     * 租金单位名称（元/㎡/月、元/月）
     */
    @ApiModelProperty(value = "租金单位名称（元/㎡/月、元/月）")
                            private String rentUnit;

    /**
     * 房源编号NCC编码
     */
    @ApiModelProperty(value = "房源编号NCC编码")
                            private String houseNoNcc;

    /**
     * 项目编码（NCC）
     */
    @ApiModelProperty(value = "项目编码（NCC）")
                            private String projectNoNcc;

    /**
     * 房屋租赁类型 01 公祖， 07 保租
     */
    @ApiModelProperty(value = "房屋租赁类型 01 公祖， 07 保租")
                            private String houseHireType;

    /**
     * 房间号
     */
    @ApiModelProperty(value = "房间号")
                            private String houseNo;

    /**
     * 床号
     */
    @ApiModelProperty(value = "床号")
                            private String bedNo;

    /**
     * 租赁类型：套，间，床
     */
    @ApiModelProperty(value = "租赁类型：套，间，床")
                            private String leaseMode;

    /**
     * 房态状态
     */
    @ApiModelProperty(value = "房态状态")
                            private String historyState;

    /**
     * 房源编号
     */
    @ApiModelProperty(value = "房源编号")
                            private String houseCode;

    /**
     * 来源节点(配租登记，看房，选房，入住)
     */
    @ApiModelProperty(value = "来源节点(配租登记，看房，选房，入住)")
                            private String sourceNode;

    /**
     * 定价策率编号
     */
    @ApiModelProperty(value = "定价策率编号")
                            private String priceSideRatioNo;

    /**
     * 定价策率名称
     */
    @ApiModelProperty(value = "定价策率名称")
                            private String priceSideRatioName;

    /**
     * 月租金规则编号（工银）
     */
    @ApiModelProperty(value = "月租金规则编号（工银）")
                            private String monthlyRentRulesNo;

    /**
     * 月租金规则名称（工银）
     */
    @ApiModelProperty(value = "月租金规则名称（工银）")
                            private String monthlyRentRulesName;

    /**
     * 公祖租金标准
     */
    @ApiModelProperty(value = "公祖租金标准")
                            private Double publicRentStandard;

    /**
     * 市场租金标准
     */
    @ApiModelProperty(value = "市场租金标准")
                            private Double marketRentStandard;

    /**
     * 人才租金标准
     */
    @ApiModelProperty(value = "人才租金标准")
                            private Double talentRentStandard;

                            private String ext1;

                            private String ext2;

                            private String ext3;

                            private String ext4;

                            private String ext5;

    /**
     * 是否有燃气费(0.否 1.是)
     */
    @ApiModelProperty(value = "是否有燃气费(0.否 1.是)")
                            private String heatingPayment;

    /**
     * 行政部门审核房号
     */
    @ApiModelProperty(value = "行政部门审核房号")
                            private String houseAuditCode;

    /**
     * 主体结构
     */
    @ApiModelProperty(value = "主体结构")
                            private String majorStructure;

    /**
     * 预测或实测
     */
    @ApiModelProperty(value = "预测或实测")
                            private String measurement;

    /**
     * 单价
     */
    @ApiModelProperty(value = "单价")
                            private Double unitPrice;

                            private String ext6;

    /**
     * 数据更新时间
     */
    @ApiModelProperty(value = "数据更新时间")
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "数据更新时间不能为空",groups = {UpdateValidatorGroup.class,InsertValidatorGroup.class})
                    private Date sjgxsj;

    /**
     * 无效字段
     */
    @ApiModelProperty(value = "无效字段")
                            private String projectid;

    /**
     * @return 主键
     */
    public String getSubjectMatterId() {
        return subjectMatterId;
    }

    public void setSubjectMatterId(String subjectMatterId) {
        this.subjectMatterId = subjectMatterId;
    }

    /**
     * @return 合同变更表主键id
     */
    public String getCcId() {
        return ccId;
    }

    public void setCcId(String ccId) {
        this.ccId = ccId;
    }

    /**
     * @return 面积类型(1.建筑面积 2.套内建筑面积)
     */
    public String getAreaType() {
        return areaType;
    }

    public void setAreaType(String areaType) {
        this.areaType = areaType;
    }

    /**
     * @return 关系表ID
     */
    public String getRelationId() {
        return relationId;
    }

    public void setRelationId(String relationId) {
        this.relationId = relationId;
    }

    /**
     * @return 合同id
     */
    public String getContractId() {
        return contractId;
    }

    public void setContractId(String contractId) {
        this.contractId = contractId;
    }

    /**
     * @return 合同编号
     */
    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    /**
     * @return 产品名称
     */
    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    /**
     * @return 产品地址
     */
    public String getProductAddress() {
        return productAddress;
    }

    public void setProductAddress(String productAddress) {
        this.productAddress = productAddress;
    }

    /**
     * @return 产品编号
     */
    public String getProductNo() {
        return productNo;
    }

    public void setProductNo(String productNo) {
        this.productNo = productNo;
    }

    /**
     * @return 项目ID
     */
    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    /**
     * @return 项目编号（业务上没有使用）
     */
    public String getProjectNo() {
        return projectNo;
    }

    public void setProjectNo(String projectNo) {
        this.projectNo = projectNo;
    }

    /**
     * @return 项目名称
     */
    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    /**
     * @return 是否空置（0否，1是）
     */
    public String getVacantStatus() {
        return vacantStatus;
    }

    public void setVacantStatus(String vacantStatus) {
        this.vacantStatus = vacantStatus;
    }

    /**
     * @return 保障房-租金标准类型(01.公租租金标准 02.人才租金标准 03.市场租金标准 04.一口价)
     */
    public String getRentStandardNo() {
        return rentStandardNo;
    }

    public void setRentStandardNo(String rentStandardNo) {
        this.rentStandardNo = rentStandardNo;
    }

    /**
     * @return 保障房-租金标准类型
     */
    public String getRentStandardName() {
        return rentStandardName;
    }

    public void setRentStandardName(String rentStandardName) {
        this.rentStandardName = rentStandardName;
    }

    /**
     * @return 租金标准（元/m²/月）金额
     */
    public Double getRent() {
        return rent;
    }

    public void setRent(Double rent) {
        this.rent = rent;
    }

    /**
     * @return 租金
     */
    public Double getLeaseTermRent() {
        return leaseTermRent;
    }

    public void setLeaseTermRent(Double leaseTermRent) {
        this.leaseTermRent = leaseTermRent;
    }

    /**
     * @return 租期
     */
    public String getLeaseTerm() {
        return leaseTerm;
    }

    public void setLeaseTerm(String leaseTerm) {
        this.leaseTerm = leaseTerm;
    }

    /**
     * @return 保证金
     */
    public Double getDeposit() {
        return deposit;
    }

    public void setDeposit(Double deposit) {
        this.deposit = deposit;
    }

    /**
     * @return 选房渠道
     */
    public String getHouseSelectionChannelsNo() {
        return houseSelectionChannelsNo;
    }

    public void setHouseSelectionChannelsNo(String houseSelectionChannelsNo) {
        this.houseSelectionChannelsNo = houseSelectionChannelsNo;
    }

    /**
     * @return 选房渠道名称
     */
    public String getHouseSelectionChannelsName() {
        return houseSelectionChannelsName;
    }

    public void setHouseSelectionChannelsName(String houseSelectionChannelsName) {
        this.houseSelectionChannelsName = houseSelectionChannelsName;
    }

    /**
     * @return 小区或楼宇
     */
    public String getCommunityBuildingNo() {
        return communityBuildingNo;
    }

    public void setCommunityBuildingNo(String communityBuildingNo) {
        this.communityBuildingNo = communityBuildingNo;
    }

    /**
     * @return 小区或楼宇名称
     */
    public String getCommunityBuildingName() {
        return communityBuildingName;
    }

    public void setCommunityBuildingName(String communityBuildingName) {
        this.communityBuildingName = communityBuildingName;
    }

    /**
     * @return 组团
     */
    public String getGroupNo() {
        return groupNo;
    }

    public void setGroupNo(String groupNo) {
        this.groupNo = groupNo;
    }

    /**
     * @return 组团名称
     */
    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    /**
     * @return 楼宇编号
     */
    public String getBuildingNo() {
        return buildingNo;
    }

    public void setBuildingNo(String buildingNo) {
        this.buildingNo = buildingNo;
    }

    /**
     * @return 楼宇名称
     */
    public String getBuildingName() {
        return buildingName;
    }

    public void setBuildingName(String buildingName) {
        this.buildingName = buildingName;
    }

    /**
     * @return 单元号
     */
    public String getUnitNo() {
        return unitNo;
    }

    public void setUnitNo(String unitNo) {
        this.unitNo = unitNo;
    }

    /**
     * @return 单元名称
     */
    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    /**
     * @return 户型类别编码
     */
    public String getHouseTypeNo() {
        return houseTypeNo;
    }

    public void setHouseTypeNo(String houseTypeNo) {
        this.houseTypeNo = houseTypeNo;
    }

    /**
     * @return 户型类别
     */
    public String getHouseTypeName() {
        return houseTypeName;
    }

    public void setHouseTypeName(String houseTypeName) {
        this.houseTypeName = houseTypeName;
    }

    /**
     * @return 建筑面积
     */
    public String getHouseStructArea() {
        return houseStructArea;
    }

    public void setHouseStructArea(String houseStructArea) {
        this.houseStructArea = houseStructArea;
    }

    /**
     * @return 套内建筑面积
     */
    public String getInnerSleeveArea() {
        return innerSleeveArea;
    }

    public void setInnerSleeveArea(String innerSleeveArea) {
        this.innerSleeveArea = innerSleeveArea;
    }

    /**
     * @return 房屋使用面积
     */
    public String getUseArea() {
        return useArea;
    }

    public void setUseArea(String useArea) {
        this.useArea = useArea;
    }

    /**
     * @return 套型
     */
    public String getJacketed() {
        return jacketed;
    }

    public void setJacketed(String jacketed) {
        this.jacketed = jacketed;
    }

    /**
     * @return 套型编号
     */
    public String getJacketedCode() {
        return jacketedCode;
    }

    public void setJacketedCode(String jacketedCode) {
        this.jacketedCode = jacketedCode;
    }

    /**
     * @return 房间号
     */
    public String getRoomNo() {
        return roomNo;
    }

    public void setRoomNo(String roomNo) {
        this.roomNo = roomNo;
    }

    /**
     * @return 房间名称
     */
    public String getRoomName() {
        return roomName;
    }

    public void setRoomName(String roomName) {
        this.roomName = roomName;
    }

    /**
     * @return 所在区县
     */
    public String getCommunityRegion() {
        return communityRegion;
    }

    public void setCommunityRegion(String communityRegion) {
        this.communityRegion = communityRegion;
    }

    /**
     * @return 小区地址
     */
    public String getCommunityAddress() {
        return communityAddress;
    }

    public void setCommunityAddress(String communityAddress) {
        this.communityAddress = communityAddress;
    }

    /**
     * @return 产品扩展
     */
    public String getProductExtend() {
        return productExtend;
    }

    public void setProductExtend(String productExtend) {
        this.productExtend = productExtend;
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * @return 保障房-标的业务分类1
     */
    public String getProductBussinessType1() {
        return productBussinessType1;
    }

    public void setProductBussinessType1(String productBussinessType1) {
        this.productBussinessType1 = productBussinessType1;
    }

    /**
     * @return 保障房-标的业务分类2
     */
    public String getProductBussinessType2() {
        return productBussinessType2;
    }

    public void setProductBussinessType2(String productBussinessType2) {
        this.productBussinessType2 = productBussinessType2;
    }

    /**
     * @return 保障房-标的业务分类3
     */
    public String getProductBussinessType3() {
        return productBussinessType3;
    }

    public void setProductBussinessType3(String productBussinessType3) {
        this.productBussinessType3 = productBussinessType3;
    }

    /**
     * @return 所在层次
     */
    public String getCurrentFloorNo() {
        return currentFloorNo;
    }

    public void setCurrentFloorNo(String currentFloorNo) {
        this.currentFloorNo = currentFloorNo;
    }

    /**
     * @return 总层数
     */
    public String getTotalFloorNo() {
        return totalFloorNo;
    }

    public void setTotalFloorNo(String totalFloorNo) {
        this.totalFloorNo = totalFloorNo;
    }

    /**
     * @return 房屋朝向
     */
    public String getHouseOrientation() {
        return houseOrientation;
    }

    public void setHouseOrientation(String houseOrientation) {
        this.houseOrientation = houseOrientation;
    }

    /**
     * @return 项目简称
     */
    public String getProjectShortName() {
        return projectShortName;
    }

    public void setProjectShortName(String projectShortName) {
        this.projectShortName = projectShortName;
    }

    /**
     * @return 运营主体类型
     */
    public String getOperateEntityType() {
        return operateEntityType;
    }

    public void setOperateEntityType(String operateEntityType) {
        this.operateEntityType = operateEntityType;
    }

    /**
     * @return 运营单位
     */
    public String getOperateEntityName() {
        return operateEntityName;
    }

    public void setOperateEntityName(String operateEntityName) {
        this.operateEntityName = operateEntityName;
    }

    /**
     * @return 运营单位业务中台编号
     */
    public String getOperateUnitBusinessNo() {
        return operateUnitBusinessNo;
    }

    public void setOperateUnitBusinessNo(String operateUnitBusinessNo) {
        this.operateUnitBusinessNo = operateUnitBusinessNo;
    }

    /**
     * @return 运营单位编号（NCC）
     */
    public String getOperateUnitNo() {
        return operateUnitNo;
    }

    public void setOperateUnitNo(String operateUnitNo) {
        this.operateUnitNo = operateUnitNo;
    }

    /**
     * @return 运营单位名称
     */
    public String getOperateUnitName() {
        return operateUnitName;
    }

    public void setOperateUnitName(String operateUnitName) {
        this.operateUnitName = operateUnitName;
    }

    /**
     * @return 项目区域业务中台编号
     */
    public String getProjectAreaBusinessNo() {
        return projectAreaBusinessNo;
    }

    public void setProjectAreaBusinessNo(String projectAreaBusinessNo) {
        this.projectAreaBusinessNo = projectAreaBusinessNo;
    }

    /**
     * @return 项目区域编号（NCC）
     */
    public String getProjectAreaNo() {
        return projectAreaNo;
    }

    public void setProjectAreaNo(String projectAreaNo) {
        this.projectAreaNo = projectAreaNo;
    }

    /**
     * @return 项目区域名称
     */
    public String getProjectAreaName() {
        return projectAreaName;
    }

    public void setProjectAreaName(String projectAreaName) {
        this.projectAreaName = projectAreaName;
    }

    /**
     * @return 项目业态（01 公租房 02 保租房 03 商业 04 共有产权房 05 仓储 06车位）
     */
    public String getProjectFormat() {
        return projectFormat;
    }

    public void setProjectFormat(String projectFormat) {
        this.projectFormat = projectFormat;
    }

    /**
     * @return 所在小区或楼宇名称
     */
    public String getProjectEstate() {
        return projectEstate;
    }

    public void setProjectEstate(String projectEstate) {
        this.projectEstate = projectEstate;
    }

    /**
     * @return 居室名称
     */
    public String getBedRoom() {
        return bedRoom;
    }

    public void setBedRoom(String bedRoom) {
        this.bedRoom = bedRoom;
    }

    /**
     * @return 租金单位名称（元/㎡/月、元/月）
     */
    public String getRentUnit() {
        return rentUnit;
    }

    public void setRentUnit(String rentUnit) {
        this.rentUnit = rentUnit;
    }

    /**
     * @return 房源编号NCC编码
     */
    public String getHouseNoNcc() {
        return houseNoNcc;
    }

    public void setHouseNoNcc(String houseNoNcc) {
        this.houseNoNcc = houseNoNcc;
    }

    /**
     * @return 项目编码（NCC）
     */
    public String getProjectNoNcc() {
        return projectNoNcc;
    }

    public void setProjectNoNcc(String projectNoNcc) {
        this.projectNoNcc = projectNoNcc;
    }

    /**
     * @return 房屋租赁类型 01 公祖， 07 保租
     */
    public String getHouseHireType() {
        return houseHireType;
    }

    public void setHouseHireType(String houseHireType) {
        this.houseHireType = houseHireType;
    }

    /**
     * @return 房间号
     */
    public String getHouseNo() {
        return houseNo;
    }

    public void setHouseNo(String houseNo) {
        this.houseNo = houseNo;
    }

    /**
     * @return 床号
     */
    public String getBedNo() {
        return bedNo;
    }

    public void setBedNo(String bedNo) {
        this.bedNo = bedNo;
    }

    /**
     * @return 租赁类型：套，间，床
     */
    public String getLeaseMode() {
        return leaseMode;
    }

    public void setLeaseMode(String leaseMode) {
        this.leaseMode = leaseMode;
    }

    /**
     * @return 房态状态
     */
    public String getHistoryState() {
        return historyState;
    }

    public void setHistoryState(String historyState) {
        this.historyState = historyState;
    }

    /**
     * @return 房源编号
     */
    public String getHouseCode() {
        return houseCode;
    }

    public void setHouseCode(String houseCode) {
        this.houseCode = houseCode;
    }

    /**
     * @return 来源节点(配租登记，看房，选房，入住)
     */
    public String getSourceNode() {
        return sourceNode;
    }

    public void setSourceNode(String sourceNode) {
        this.sourceNode = sourceNode;
    }

    /**
     * @return 定价策率编号
     */
    public String getPriceSideRatioNo() {
        return priceSideRatioNo;
    }

    public void setPriceSideRatioNo(String priceSideRatioNo) {
        this.priceSideRatioNo = priceSideRatioNo;
    }

    /**
     * @return 定价策率名称
     */
    public String getPriceSideRatioName() {
        return priceSideRatioName;
    }

    public void setPriceSideRatioName(String priceSideRatioName) {
        this.priceSideRatioName = priceSideRatioName;
    }

    /**
     * @return 月租金规则编号（工银）
     */
    public String getMonthlyRentRulesNo() {
        return monthlyRentRulesNo;
    }

    public void setMonthlyRentRulesNo(String monthlyRentRulesNo) {
        this.monthlyRentRulesNo = monthlyRentRulesNo;
    }

    /**
     * @return 月租金规则名称（工银）
     */
    public String getMonthlyRentRulesName() {
        return monthlyRentRulesName;
    }

    public void setMonthlyRentRulesName(String monthlyRentRulesName) {
        this.monthlyRentRulesName = monthlyRentRulesName;
    }

    /**
     * @return 公祖租金标准
     */
    public Double getPublicRentStandard() {
        return publicRentStandard;
    }

    public void setPublicRentStandard(Double publicRentStandard) {
        this.publicRentStandard = publicRentStandard;
    }

    /**
     * @return 市场租金标准
     */
    public Double getMarketRentStandard() {
        return marketRentStandard;
    }

    public void setMarketRentStandard(Double marketRentStandard) {
        this.marketRentStandard = marketRentStandard;
    }

    /**
     * @return 人才租金标准
     */
    public Double getTalentRentStandard() {
        return talentRentStandard;
    }

    public void setTalentRentStandard(Double talentRentStandard) {
        this.talentRentStandard = talentRentStandard;
    }

    /**
     * @return 
     */
    public String getExt1() {
        return ext1;
    }

    public void setExt1(String ext1) {
        this.ext1 = ext1;
    }

    /**
     * @return 
     */
    public String getExt2() {
        return ext2;
    }

    public void setExt2(String ext2) {
        this.ext2 = ext2;
    }

    /**
     * @return 
     */
    public String getExt3() {
        return ext3;
    }

    public void setExt3(String ext3) {
        this.ext3 = ext3;
    }

    /**
     * @return 
     */
    public String getExt4() {
        return ext4;
    }

    public void setExt4(String ext4) {
        this.ext4 = ext4;
    }

    /**
     * @return 
     */
    public String getExt5() {
        return ext5;
    }

    public void setExt5(String ext5) {
        this.ext5 = ext5;
    }

    /**
     * @return 是否有燃气费(0.否 1.是)
     */
    public String getHeatingPayment() {
        return heatingPayment;
    }

    public void setHeatingPayment(String heatingPayment) {
        this.heatingPayment = heatingPayment;
    }

    /**
     * @return 行政部门审核房号
     */
    public String getHouseAuditCode() {
        return houseAuditCode;
    }

    public void setHouseAuditCode(String houseAuditCode) {
        this.houseAuditCode = houseAuditCode;
    }

    /**
     * @return 主体结构
     */
    public String getMajorStructure() {
        return majorStructure;
    }

    public void setMajorStructure(String majorStructure) {
        this.majorStructure = majorStructure;
    }

    /**
     * @return 预测或实测
     */
    public String getMeasurement() {
        return measurement;
    }

    public void setMeasurement(String measurement) {
        this.measurement = measurement;
    }

    /**
     * @return 单价
     */
    public Double getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(Double unitPrice) {
        this.unitPrice = unitPrice;
    }

    /**
     * @return 
     */
    public String getExt6() {
        return ext6;
    }

    public void setExt6(String ext6) {
        this.ext6 = ext6;
    }

    /**
     * @return 数据更新时间
     */
    public Date getSjgxsj(){
        if(sjgxsj!=null){
            return (Date)sjgxsj.clone();
        }else{
            return null;
        }
    }

    public void setSjgxsj(Date sjgxsj) {
        if(sjgxsj==null){
            this.sjgxsj = null;
        }else{
            this.sjgxsj = (Date)sjgxsj.clone();
        }
    }

    /**
     * @return 无效字段
     */
    public String getProjectid() {
        return projectid;
    }

    public void setProjectid(String projectid) {
        this.projectid = projectid;
    }

      @Override
    public String toString() {
        return "BbsChangeSubjectMatterPageVo{" +
            "subjectMatterId=" + subjectMatterId +
            ", ccId=" + ccId +
            ", areaType=" + areaType +
            ", relationId=" + relationId +
            ", contractId=" + contractId +
            ", contractNo=" + contractNo +
            ", productName=" + productName +
            ", productAddress=" + productAddress +
            ", productNo=" + productNo +
            ", projectId=" + projectId +
            ", projectNo=" + projectNo +
            ", projectName=" + projectName +
            ", vacantStatus=" + vacantStatus +
            ", rentStandardNo=" + rentStandardNo +
            ", rentStandardName=" + rentStandardName +
            ", rent=" + rent +
            ", leaseTermRent=" + leaseTermRent +
            ", leaseTerm=" + leaseTerm +
            ", deposit=" + deposit +
            ", houseSelectionChannelsNo=" + houseSelectionChannelsNo +
            ", houseSelectionChannelsName=" + houseSelectionChannelsName +
            ", communityBuildingNo=" + communityBuildingNo +
            ", communityBuildingName=" + communityBuildingName +
            ", groupNo=" + groupNo +
            ", groupName=" + groupName +
            ", buildingNo=" + buildingNo +
            ", buildingName=" + buildingName +
            ", unitNo=" + unitNo +
            ", unitName=" + unitName +
            ", houseTypeNo=" + houseTypeNo +
            ", houseTypeName=" + houseTypeName +
            ", houseStructArea=" + houseStructArea +
            ", innerSleeveArea=" + innerSleeveArea +
            ", useArea=" + useArea +
            ", jacketed=" + jacketed +
            ", jacketedCode=" + jacketedCode +
            ", roomNo=" + roomNo +
            ", roomName=" + roomName +
            ", communityRegion=" + communityRegion +
            ", communityAddress=" + communityAddress +
            ", productExtend=" + productExtend +
            ", delFlag=" + delFlag +
            ", productBussinessType1=" + productBussinessType1 +
            ", productBussinessType2=" + productBussinessType2 +
            ", productBussinessType3=" + productBussinessType3 +
            ", currentFloorNo=" + currentFloorNo +
            ", totalFloorNo=" + totalFloorNo +
            ", houseOrientation=" + houseOrientation +
            ", projectShortName=" + projectShortName +
            ", operateEntityType=" + operateEntityType +
            ", operateEntityName=" + operateEntityName +
            ", operateUnitBusinessNo=" + operateUnitBusinessNo +
            ", operateUnitNo=" + operateUnitNo +
            ", operateUnitName=" + operateUnitName +
            ", projectAreaBusinessNo=" + projectAreaBusinessNo +
            ", projectAreaNo=" + projectAreaNo +
            ", projectAreaName=" + projectAreaName +
            ", projectFormat=" + projectFormat +
            ", projectEstate=" + projectEstate +
            ", bedRoom=" + bedRoom +
            ", rentUnit=" + rentUnit +
            ", houseNoNcc=" + houseNoNcc +
            ", projectNoNcc=" + projectNoNcc +
            ", houseHireType=" + houseHireType +
            ", houseNo=" + houseNo +
            ", bedNo=" + bedNo +
            ", leaseMode=" + leaseMode +
            ", historyState=" + historyState +
            ", houseCode=" + houseCode +
            ", sourceNode=" + sourceNode +
            ", priceSideRatioNo=" + priceSideRatioNo +
            ", priceSideRatioName=" + priceSideRatioName +
            ", monthlyRentRulesNo=" + monthlyRentRulesNo +
            ", monthlyRentRulesName=" + monthlyRentRulesName +
            ", publicRentStandard=" + publicRentStandard +
            ", marketRentStandard=" + marketRentStandard +
            ", talentRentStandard=" + talentRentStandard +
            ", ext1=" + ext1 +
            ", ext2=" + ext2 +
            ", ext3=" + ext3 +
            ", ext4=" + ext4 +
            ", ext5=" + ext5 +
            ", heatingPayment=" + heatingPayment +
            ", houseAuditCode=" + houseAuditCode +
            ", majorStructure=" + majorStructure +
            ", measurement=" + measurement +
            ", unitPrice=" + unitPrice +
            ", ext6=" + ext6 +
            ", sjgxsj=" + sjgxsj +
            ", projectid=" + projectid +
        "}";
    }
}
