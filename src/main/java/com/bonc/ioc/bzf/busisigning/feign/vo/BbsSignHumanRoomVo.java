package com.bonc.ioc.bzf.busisigning.feign.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * 签约信息和产品列表 实体类
 *
 * <AUTHOR>
 * @since 2023/5/8
 */
@ApiModel(value = "BbsSignHumanRoomVo", description = "BbsSignHumanRoomVo签约计划信息和产品列表")
public class BbsSignHumanRoomVo implements Serializable {

    @ApiModelProperty(value = "计划id")
    private String planId;
    /**
     * 签约id
     */
    @ApiModelProperty(value = "签约id")
    private String signId;

    /**
     * 产品来源类型
     */
    @ApiModelProperty(value = "产品来源类型(导入还是别的)")
    private String productSourceType;

    /**
     * 趸租单位
     */
    @ApiModelProperty(value = "趸租单位 有就传")
    private BbsResultCustomerVo bbsResultCustomerVo;

    /**
     * 人产品
     */
    @ApiModelProperty(value = "人产品")
    List<BbsResultRelationProductCustomerVo> productList;


    public String getPlanId() {
        return planId;
    }

    public void setPlanId(String planId) {
        this.planId = planId;
    }

    public String getSignId() {
        return signId;
    }

    public void setSignId(String signId) {
        this.signId = signId;
    }

    public String getProductSourceType() {
        return productSourceType;
    }

    public void setProductSourceType(String productSourceType) {
        this.productSourceType = productSourceType;
    }

    public BbsResultCustomerVo getBbsResultCustomerVo() {
        return bbsResultCustomerVo;
    }

    public void setBbsResultCustomerVo(BbsResultCustomerVo bbsResultCustomerVo) {
        this.bbsResultCustomerVo = bbsResultCustomerVo;
    }

    public List<BbsResultRelationProductCustomerVo> getProductList() {
        return productList;
    }

    public void setProductList(List<BbsResultRelationProductCustomerVo> productList) {
        this.productList = productList;
    }
}
