package com.bonc.ioc.bzf.busisigning.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>功能描述</p>
 *
 * @author: wangw
 * @date: 2022-6-11
 */
@Data
@ToString
@ColumnWidth(26) // 表示列宽
public class SignImportVo implements Serializable {

    private static final long serialVersionUID = -769443249346441495L;

    /**
     * 业务ID
     */
    @ExcelProperty(value = "业务ID", index = 0)
    @ApiModelProperty(value = "业务ID")
    private String arrangeCustomerId;

    /**
     * 计划ID
     */
    @ExcelProperty(value = "计划ID", index = 1)
    @ApiModelProperty(value = "计划ID")
    private String planId;

    /**
     * 计划名称
     */
    @ExcelProperty(value = "计划名称", index = 2)
    @ApiModelProperty(value = "计划名称")
    private String planName;

    /**
     * 小区ID
     */
    @ExcelProperty(value = "小区ID", index = 3)
    @ApiModelProperty(value = "小区ID")
    private String communityId;

    /**
     * 小区名称
     */
    @ExcelProperty(value = "小区名称", index = 4)
    @ApiModelProperty(value = "小区名称")
    private String communityName;

    /**
     * 客户类型
     */
    @ExcelProperty(value = "客户类型", index = 5)
    @ApiModelProperty(value = "客户类型")
    private String customerType;

    /**
     * 房屋地址
     */
    @ExcelProperty(value = "房屋地址", index = 6)
    @ApiModelProperty(value = "房屋地址")
    private String houseResource;

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名", index = 7)
    @ApiModelProperty(value = "姓名")
    private String customerName;

    /**
     * 证件类型
     */
    @ExcelProperty(value = "证件类型", index = 8)
    @ApiModelProperty(value = "证件类型")
    private String certificateType;

    /**
     * 证件号码
     */
    @ExcelProperty(value = "证件号码", index = 9)
    @ApiModelProperty(value = "证件号码")
    private String certificateNum;

    /**
     * 合同状态
     */
    @ExcelProperty(value = "合同状态", index = 10)
    @ApiModelProperty(value = "合同状态")
    private String contractState;

    /**
     * 签约时间
     */
    @ApiModelProperty(value = "签约时间")
    @ExcelProperty(value = "签约时间", index = 11)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signTime;
    @ApiModelProperty(value = "合同开始时间")
    @ExcelProperty(value = "合同起始日", index = 12)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date contractBeginTime;

    @ApiModelProperty(value = "合同结束时间")
    @ExcelProperty(value = "合同终止日", index = 13)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date contractEndTime;

    /**
     * 合同编码
     */
    @ApiModelProperty(value = "合同编码")
    @ExcelProperty(value = "合同编号", index = 14)
    private String contractCode;

    /**
     * 付款方式
     */
    @ApiModelProperty(value = "付款方式")
    @ExcelProperty(value = "付款方式", index = 15)
    private String payType;

    /**
     * 月租金
     */
    @ApiModelProperty(value = "月租金")
    @ExcelProperty(value = "月租金", index = 16)
    private BigDecimal houseRent;

    /**
     * 手机号
     */
    @ExcelProperty(value = "手机号", index = 17)
    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "批次号")
    @ExcelIgnore
    private String batchId;
}
