package com.bonc.ioc.bzf.busisigning.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.bonc.ioc.common.validator.inf.InsertValidatorGroup;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 客户签约表 实体类
 *
 * <AUTHOR>
 * @date 2022-06-08
 * @change 2022-06-08 by wtl for init
 */
@Data
@ApiModel(value = "BbsiCustSignVo对象", description = "客户签约表")
public class BbsiCustSignVo extends McpBaseVo implements Serializable {


    /**
     * 签约ID
     */
    @ApiModelProperty(value = "签约ID")
    @NotBlank(message = "签约ID不能为空", groups = {UpdateValidatorGroup.class})
    private String arrangeCustomerId;

    /**
     * 安排ID
     */
    @ApiModelProperty(value = "安排ID")
    @NotBlank(message = "安排ID不能为空", groups = {UpdateValidatorGroup.class, InsertValidatorGroup.class})
    private String arrangeId;

    /**
     * 客户ID
     */
    @ApiModelProperty(value = "客户ID")
    @NotBlank(message = "客户ID不能为空", groups = {UpdateValidatorGroup.class, InsertValidatorGroup.class})
    private String customerId;

    /**
     * 计划ID
     */
    @ApiModelProperty(value = "计划ID")
    @NotBlank(message = "计划ID不能为空", groups = {UpdateValidatorGroup.class, InsertValidatorGroup.class})
    private String planId;

    /**
     * 小区ID
     */
    @ApiModelProperty(value = "小区ID")
    @NotBlank(message = "小区ID不能为空", groups = {UpdateValidatorGroup.class, InsertValidatorGroup.class})
    private String communityId;

    /**
     * 资产id
     */
    @ApiModelProperty(value = "资产id")
    private String assetId;

    /**
     * 单元号
     */
    @ApiModelProperty(value = "单元号")
    private String unitNo;

    /**
     * 楼栋号
     */
    @ApiModelProperty(value = "楼栋号")
    private String buildingNo;


    /**
     * 选房中心结果ID
     */
    @ApiModelProperty(value = "选房中心结果ID")
    @NotBlank(message = "选房中心结果ID不能为空", groups = {UpdateValidatorGroup.class, InsertValidatorGroup.class})
    private String onlineId;

    /**
     * 选房结果
     */
    @ApiModelProperty(value = "选房结果")
    private String selResult;

    /**
     * 签约时间
     */
    @ApiModelProperty(value = "签约时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signTime;

    /**
     * 签约状态 1、待签约 2、已签约 3、未签约
     */
    @ApiModelProperty(value = "签约状态 1、待签约 2、已签约 3、未签约")
    @NotBlank(message = "签约状态 1、待签约 2、已签约 3、未签约不能为空", groups = {UpdateValidatorGroup.class, InsertValidatorGroup.class})
    private String signState;

    /**
     * 合同ID
     */
    @ApiModelProperty(value = "合同ID")
    private String contractId;

    /**
     * 合同编码
     */
    @ApiModelProperty(value = "合同编码")
    private String contractCode;

    /**
     * 推送入住中心标识 1、未推送 2、已推送
     */
    @ApiModelProperty(value = "推送入住中心标识 1、未推送 2、已推送")
    @NotBlank(message = "推送入住中心标识 1、未推送 2、已推送不能为空", groups = {UpdateValidatorGroup.class, InsertValidatorGroup.class})
    private String pushCheckinState;

    /**
     * 签约通知发送状态编码 1.未发送2.已发送
     */
    @ApiModelProperty(value = "签约通知发送状态编码 1.未发送2.已发送")
    @NotBlank(message = "签约通知发送状态编码 1.未发送2.已发送不能为空", groups = {UpdateValidatorGroup.class, InsertValidatorGroup.class})
    private String signNoticeSendStatusCode;

    /**
     * 签约通知_通知消息结果编码 1.成功2失败
     */
    @ApiModelProperty(value = "签约通知_通知消息结果编码 1.成功2失败")
    private String signNoticeSendResultCode;

    /**
     * 签约通知_通知消息记录ID
     */
    @ApiModelProperty(value = "签约通知_通知消息记录ID")
    private String signNoticeSendLogId;

    /**
     * 签约通知_短信消息结果编码 1.成功2失败
     */
    @ApiModelProperty(value = "签约通知_短信消息结果编码 1.成功2失败")
    private String signMessageSendResultCode;

    /**
     * 签约通知_短信消息记录ID
     */
    @ApiModelProperty(value = "签约通知_短信消息记录ID")
    private String signMessageSendLogId;

    /**
     * 催约通知发送状态编码 1、未发送 2、已发送
     */
    @ApiModelProperty(value = "催约通知发送状态编码 1、未发送 2、已发送")
    @NotBlank(message = "催约通知发送状态编码 1、未发送 2、已发送不能为空", groups = {UpdateValidatorGroup.class, InsertValidatorGroup.class})
    private String urgeNoticeSendStatusCode;

    /**
     * 催约通知_通知消息结果编码 1.成功2失败
     */
    @ApiModelProperty(value = "催约通知_通知消息结果编码 1.成功2失败")
    private String urgeNoticeSendResultCode;

    /**
     * 催约通知_通知消息记录ID
     */
    @ApiModelProperty(value = "催约通知_通知消息记录ID")
    private String urgeNoticeSendLogId;

    /**
     * 催约通知_短信消息结果编码 1.成功2失败
     */
    @ApiModelProperty(value = "催约通知_短信消息结果编码 1.成功2失败")
    private String urgeMessageSendResultCode;

    /**
     * 催约通知_短信消息记录ID
     */
    @ApiModelProperty(value = "催约通知_短信消息记录ID")
    private String urgeMessageSendLogId;

    @Override
    public String toString() {
        return "BbsiCustSignVo{" +
                "arrangeCustomerId='" + arrangeCustomerId + '\'' +
                ", arrangeId='" + arrangeId + '\'' +
                ", customerId='" + customerId + '\'' +
                ", planId='" + planId + '\'' +
                ", communityId='" + communityId + '\'' +
                ", assetId='" + assetId + '\'' +
                ", unitNo='" + unitNo + '\'' +
                ", buildingNo='" + buildingNo + '\'' +
                ", onlineId='" + onlineId + '\'' +
                ", selResult='" + selResult + '\'' +
                ", signTime=" + signTime +
                ", signState='" + signState + '\'' +
                ", contractId='" + contractId + '\'' +
                ", contractCode='" + contractCode + '\'' +
                ", pushCheckinState='" + pushCheckinState + '\'' +
                ", signNoticeSendStatusCode='" + signNoticeSendStatusCode + '\'' +
                ", signNoticeSendResultCode='" + signNoticeSendResultCode + '\'' +
                ", signNoticeSendLogId='" + signNoticeSendLogId + '\'' +
                ", signMessageSendResultCode='" + signMessageSendResultCode + '\'' +
                ", signMessageSendLogId='" + signMessageSendLogId + '\'' +
                ", urgeNoticeSendStatusCode='" + urgeNoticeSendStatusCode + '\'' +
                ", urgeNoticeSendResultCode='" + urgeNoticeSendResultCode + '\'' +
                ", urgeNoticeSendLogId='" + urgeNoticeSendLogId + '\'' +
                ", urgeMessageSendResultCode='" + urgeMessageSendResultCode + '\'' +
                ", urgeMessageSendLogId='" + urgeMessageSendLogId + '\'' +
                '}';
    }
}
