package com.bonc.ioc.bzf.busisigning.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>功能描述</p>
 *
 * @author: wangw
 * @date: 2022-6-9
 */
@Data
public class ChooseDetailVo extends McpBaseVo implements Serializable {

    private static final long serialVersionUID = -2231163382791839469L;

    /**
     * 客户姓名
     */
    @ApiModelProperty(value = "客户姓名")
    private String customerName;
    /**
     * 证件类型
     */
    @ApiModelProperty(value = "证件类型")
    private String certificateType;
    /**
     * 证件编号
     */
    @ApiModelProperty(value = "证件编号")
    private String certificateNum;
    /**
     * 性别
     */
    @ApiModelProperty(value = "客户性别,MAN:男；FEMALE:女")
    private String customerGenderCode;

    @ApiModelProperty(value = "客户性别名称")
    private String customerGenderName;
    /**
     * 手机
     */
    @ApiModelProperty(value = "手机")
    private String customerPhone;
    /**
     * 婚姻
     */
    @ApiModelProperty(value = "婚姻")
    private String maritalStatus;
    /**
     * 院校名称
     */
    @ApiModelProperty(value = "院校名称")
    private String collegeName;
    /**
     * 专业名称
     */
    @ApiModelProperty(value = "专业名称")
    private String profession;
    /**
     * 学历
     */
    @ApiModelProperty(value = "学历")
    private String education;
    /**
     * 毕业时间
     */
    @ApiModelProperty(value = "毕业时间")
    private String graduateTime;
    /**
     * 毕业证编号
     */
    @ApiModelProperty(value = "毕业证编号")
    private String diplomaNumber;
    /**
     * 工作单位
     */
    @ApiModelProperty(value = "工作单位")
    private String company;
    /**
     * 单位地址
     */
    @ApiModelProperty(value = "单位地址")
    private String companyAddress;
    /**
     * 登记时间
     */
    @ApiModelProperty(value = "登记时间")
    private Date registationTime;
    /**
     * 看房时间
     */
    @ApiModelProperty(value = "看房时间")
    private Date seeHouseTime;
    /**
     * 选房时间
     */
    @ApiModelProperty(value = "选房时间")
    private Date selHouseTime;
    /**
     * 房源
     */
    @ApiModelProperty(value = "房源")
    private String houseSource;
    /**
     * 租金
     */
    @ApiModelProperty(value = "租金")
    private Double houseRent;
}
