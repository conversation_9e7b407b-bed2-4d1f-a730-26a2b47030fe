package com.bonc.ioc.bzf.busisigning.utils;

import java.util.Calendar;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/5/24
 * @change 2022/5/24 by wangxiaofei for init
 **/
public class IdCardCheckUtil {
    private static final int ID_LENGTH = 17;

    /**
     * 判断身份证格式是否正确
     *
     * @param idNum 身份证
     * @return 响应数据
     */
    public static boolean vId(String idNum) {
        return vIDNumByRegex(idNum) && vIDNumByCode(idNum);
    }

    public static boolean vIDNumByRegex(String idNum) {
        try {
            String curYear = "" + Calendar.getInstance().get(Calendar.YEAR);
            int y3 = Integer.parseInt(curYear.substring(2, 3));
            int y4 = Integer.parseInt(curYear.substring(3, 4));
            return idNum.matches("^(1[1-5]|2[1-3]|3[1-7]|4[1-6]|5[0-4]|6[1-5]|71|8[1-2])\\d{4}(19\\d{2}|20([0-" + (y3 - 1) + "][0-9]|" + y3 + "[0-" + y4
                    + "]))(((0[1-9]|1[0-2])(0[1-9]|[1-2][0-9]|3[0-1])))\\d{3}([0-9]|x|X)$");
        } catch (Exception e) {
            return Boolean.FALSE;
        }

    }

    public static boolean vIDNumByCode(String idNum) {
        try {
            // 系数列表
            int[] ratioArr = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};

            // 校验码列表
            char[] checkCodeList = {'1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'};

            // 获取身份证号字符数组
            char[] cIds = idNum.toCharArray();

            if (cIds.length != 18) {
                return false;
            }

            // 获取最后一位（身份证校验码）
            char oCode = cIds[ID_LENGTH];
            int[] iIds = new int[ID_LENGTH];
            // 身份证号第1-17位于系数之积的和
            int idSum = 0;
            // 余数(用加出来和除以11，看余数是多少？)
            int residue = 0;

            for (int i = 0; i < ID_LENGTH; i++) {
                iIds[i] = cIds[i] - '0';
                idSum += iIds[i] * ratioArr[i];
            }
            // 取得余数
            residue = idSum % 11;

            return Character.toUpperCase(oCode) == checkCodeList[residue];
        } catch (Exception e) {
            return Boolean.FALSE;
        }
    }

    public static void main(String[] args) {
        boolean id = IdCardCheckUtil.vId("");
        System.out.println(id);
    }

}
