package com.bonc.ioc.bzf.busisigning.workflow.component;

import com.bonc.ioc.bzf.busisigning.dao.BbsResultProductMapper;
import com.bonc.ioc.bzf.busisigning.entity.BbsResultProductEntity;
import com.bonc.ioc.bzf.busisigning.feign.feign.BbctContractFeignClient;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractManagementVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractSubjectMatterVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsRenewalProductVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsResultProductVo;
import com.bonc.ioc.common.exception.McpException;
import com.bonc.ioc.common.util.AppReply;
import com.bonc.ioc.common.util.CollectionUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 工作流使用的工具
 */
@Slf4j
@Data
@Component
public class WorkflowCommponent {

    @Resource
    private BbctContractFeignClient contractFeignClient;

    @Resource
    private BbsResultProductMapper bbsResultProductMapper;

    /**
     * 获取流程名称
     * @param contractNo
     * @param type 1：补充协议审签 2：上传结果审核 3：退款审核 4：追加账单审核
     * @return
     */
    public String getWorkName(String contractNo,String type){
        List<BbctContractSubjectMatterVo> list = this.houseMesage(contractNo);
        String name = this.setName(list,type);
        return name;
    }

    /**
     * 设置名称
     * @param list
     * @param type 1：补充协议审签 2：上传结果审核 3：退款审核 4：追加账单审核
     * @return
     */
    public String setName(List<BbctContractSubjectMatterVo> list ,String type){
        StringBuffer msg = new StringBuffer();
        BbctContractSubjectMatterVo subjectMatterVo = list.get(0);
        msg.append(subjectMatterVo.getProjectName())
                .append(subjectMatterVo.getBuildingName()).append("号楼")
                .append(subjectMatterVo.getHouseNo());
        if(list.size() > 1){
            msg.append("等");
        }
        if("1".equals(type)){
            msg.append("补充协议审签");
        }else if("2".equals(type)){
            msg.append("上传结果审核");
        }else if("3".equals(type)){
            msg.append("退款审核");
        }else if("4".equals(type)){
            msg.append("追加账单审核");
        }else{
            throw new McpException("错误的分类");
        }
        return msg.toString();
    }


    private List<BbctContractSubjectMatterVo> houseMesage(String contractNo){
        if(StringUtils.isNotBlank(contractNo)) {
            AppReply<BbctContractManagementVo> appReply = this.contractFeignClient.selectByIdNo(contractNo, null);
            if (appReply != null && appReply.getData() != null && AppReply.SUCCESS_CODE.equals(appReply.getCode())) {
                List<BbctContractSubjectMatterVo> list = appReply.getData().getSubjectMatterList();
                if(CollectionUtil.isNotEmpty(list)){
                    return list;
                }else{
                    throw new McpException("获取合同信息失败,房源信息委空:" + contractNo);
                }
            } else {
                throw new McpException("获取合同信息失败:" + contractNo);
            }
        }else{
            throw new McpException("获取合同信息失败:合同编号不能为空" );
        }
    }

    /**
     * 新签获取流程名称
     * @param bbsResultProductVoList
     * @return
     */
    public String signName(List<BbsResultProductVo> bbsResultProductVoList){
        StringBuilder name = new StringBuilder();
        if(CollectionUtil.isNotEmpty(bbsResultProductVoList)){
            BbsResultProductVo vo = bbsResultProductVoList.get(0);
            name.append(vo.getProjectName())
                    .append(vo.getBuildingName()).append("号楼")
                    .append(vo.getHouseNo());
            if(bbsResultProductVoList.size() > 1){
                name.append("等");
            }
            name.append("合同审签（新签）");
        }else{
            throw new McpException("签约房源不能为空");
        }
        log.info("签约合同流程名称："+name);
        return name.toString();
    }

    /**
     * 合同审签流程名称
     * @param signId
     * @return
     */
    public String signContractName(String signId){
        StringBuilder name = new StringBuilder();
        List<BbsResultProductEntity> bbsResultProductVoList = this.bbsResultProductMapper.selectBySignId(signId);
        if(CollectionUtil.isNotEmpty(bbsResultProductVoList)){
            BbsResultProductEntity vo = bbsResultProductVoList.get(0);
            name.append(vo.getProjectName())
                    .append(vo.getBuildingName()).append("号楼")
                    .append(vo.getHouseNo());
            if(bbsResultProductVoList.size() > 1){
                name.append("等");
            }
            name.append("合同审核（新签）");
        }else{
            throw new McpException("签约房源不能为空");
        }
        log.info("签约合同流程名称："+name);
        return name.toString();
    }

    /**
     * 续签合同审签流程名称
     * @param signId
     * @return
     */
    public String renewalContractName(String signId){
        StringBuilder name = new StringBuilder();
        List<BbsResultProductEntity> bbsResultProductVoList = this.bbsResultProductMapper.selectRenewalBySignId(signId);
        if(CollectionUtil.isNotEmpty(bbsResultProductVoList)){
            BbsResultProductEntity vo = bbsResultProductVoList.get(0);
            name.append(vo.getProjectName())
                    .append(vo.getBuildingName()).append("号楼")
                    .append(vo.getHouseNo());
            if(bbsResultProductVoList.size() > 1){
                name.append("等");
            }
            name.append("合同审核（续签）");
        }else{
            throw new McpException("签约房源不能为空");
        }
        log.info("签约合同流程名称："+name);
        return name.toString();
    }

    /**
     * 续签获取流程名称
     * @param bbsResultProductVoList
     * @return
     */
    public String renewalName(List<BbsRenewalProductVo> bbsResultProductVoList){
        StringBuilder name = new StringBuilder();
        if(CollectionUtil.isNotEmpty(bbsResultProductVoList)){
            BbsRenewalProductVo vo = bbsResultProductVoList.get(0);
            name.append(vo.getProjectName())
                    .append(vo.getBuildingName()).append("号楼")
                    .append(vo.getHouseNo());
            if(bbsResultProductVoList.size() > 1){
                name.append("等");
            }
            name.append("合同审签（续签）");
        }else{
            throw new McpException("签约房源不能为空");
        }
        log.info("签约合同流程名称："+name);
        return name.toString();
    }
}
