package com.bonc.ioc.bzf.busisigning.enums;

/**
 * 账单类型 枚举类
 *
 * <AUTHOR>
 * @since 2024/10/30
 */
public enum BillTypeEnum {

    /**
     * 租金
     */
    RENT("1", "租金"),

    /**
     * 保证金
     */
    CASH_PLEDGE("2", "保证金"),

    /**
     * 月租金
     */
    MONTH_RENT("3", "月租金"),

    /**
     * 抵扣
     */
    DEDUCTION("4", "抵扣"),

    /**
     * 抵扣
     */
    PROP("5", "物业费"),

    /**
     * 物业费抵扣
     */
    PROP_DEDUCTION("6", "物业费抵扣");

    /**
     * 编号
     */
    private String code;

    /**
     * 描述信息
     */
    private String desc;

    /**
     * 获取编号
     *
     * @return 编号
     */
    public String getCode() {
        return this.code;
    }

    /**
     * 获取描述信息
     *
     * @return 描述信息
     */
    public String getDesc() {
        return this.desc;
    }

    /**
     * 构造方法
     *
     * @param code 编号
     * @param desc 描述信息
     */
    BillTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 构造方法
     */
    BillTypeEnum() {
    }

    /**
     * 根据编号查询描述信息
     *
     * @param code 编号
     * @return 描述信息
     */
    public static String getDescByCode(String code) {
        BillTypeEnum[] enums = values();
        for (BillTypeEnum en : enums) {
            if (en.getCode().equals(code)) {
                return en.getDesc();
            }
        }
        return null;
    }

    /**
     * 根据描述信息查询编号
     *
     * @param desc 描述信息
     * @return 编号
     */
    public static String getCodeByDesc(String desc) {
        BillTypeEnum[] enums = values();
        for (BillTypeEnum en : enums) {
            if (en.getDesc().equals(desc)) {
                return en.getCode();
            }
        }
        return null;
    }
}
