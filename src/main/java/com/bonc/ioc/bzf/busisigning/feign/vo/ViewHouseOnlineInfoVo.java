package com.bonc.ioc.bzf.busisigning.feign.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 看房登记表 实体类
 *
 * <AUTHOR>
 * @date 2022-06-02
 * @change 2022-06-02 by wxf for init
 */
@Data
@ApiModel(value = "ViewHouseOnlineInfoVo对象", description = "看房登记表")
public class ViewHouseOnlineInfoVo extends McpBaseVo implements Serializable {


    /**
     * 客户预约看房ID
     */
    @ApiModelProperty(value = "客户预约看房ID")
    @NotBlank(message = "客户预约看房ID不能为空", groups = {UpdateValidatorGroup.class})
    private String onlineId;

    /**
     * 看房信息ID
     */
    @ApiModelProperty(value = "看房信息ID")
    private String viewInfoId;

    /**
     * 客户ID
     */
    @ApiModelProperty(value = "客户ID")
    private String customId;

    /**
     * 小区ID
     */
    @ApiModelProperty(value = "小区ID")
    private String communityId;

    /**
     * 小区ID
     */
    @ApiModelProperty(value = "计划ID")
    private String planId;

    /**
     * 看房规则详情ID
     */
    @ApiModelProperty(value = "看房规则详情ID")
    private String ruleInfoId;

    /**
     * 看房开始时间
     */
    @ApiModelProperty(value = "看房开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 看房结束时间
     */
    @ApiModelProperty(value = "看房结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 看房地点
     */
    @ApiModelProperty(value = "看房地点")
    private String viewHouseAddr;

    /**
     * 预约看房成功时间
     */
    @ApiModelProperty(value = "预约看房成功时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date viewTime;
    /**
     * 预约看房成功时间
     */
    @ApiModelProperty(value = "现场看房时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date actualViewTime;

}
