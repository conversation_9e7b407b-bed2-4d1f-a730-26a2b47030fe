package com.bonc.ioc.bzf.busisigning.feign.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value = "BbsSignForAppVo对象", description = "签约app表")
@Data
public class BbsSignVo implements Serializable {

    @ApiModelProperty(value = "产品类型(1.公租房 2.包租房)")
    private String productType;

    @ApiModelProperty(value = "签约类型(1.散租 2.趸租 3.管理协议)")
    private String signType;

    @ApiModelProperty(value = "签约计划id(0.无计划)")
    private String planId;

    @ApiModelProperty(value = "签约状态(1.暂存 2.待签约 3.已签约 4.未签约)")
    private String signStatus;

    @ApiModelProperty(value = "散户或者企业编号")
    private String customerNo;

    @ApiModelProperty(value = "是否支持线上签约(0.否 1.是)")
    private String supportOnlineSign;

    @ApiModelProperty(value = "签约状态逗号分隔")
    private String signStatusStr;

}
