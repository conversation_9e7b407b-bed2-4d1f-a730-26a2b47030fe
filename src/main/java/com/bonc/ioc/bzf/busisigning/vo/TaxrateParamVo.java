package com.bonc.ioc.bzf.busisigning.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/9/11
 */
@Data
@ApiModel(value = "税率查询参数", description = "税率查询参数")
public class TaxrateParamVo {

    @ApiModelProperty(value = "项目ID")
    private String projectId;
    @ApiModelProperty(value = "项目业态 03:商业")
    private String yeTai;
    @ApiModelProperty(value = "收费项目 01:房屋租金 07:物业费")
    private String chargeItemId;
    @ApiModelProperty(value = "承租方 02:企业 03:个人 ")
    private String tenantry;
    @ApiModelProperty(value = "企业ID 如果承租方=02企业，则企业ID必填")
    private String companyId;
    @ApiModelProperty(value = "当前页码")
    private String current;
    @ApiModelProperty(value = "每页最大数目")
    private String size;
    @ApiModelProperty(value = "是否全量查询")
    private String fullPage;

}
