package com.bonc.ioc.bzf.busisigning.vo;

import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 签约明细审核日志表V3.0 实体类
 *
 * <AUTHOR>
 * @date 2022-12-07
 * @change 2022-12-07 by ly for init
 */
@ApiModel(value = "BbsiRuleResultLogInfoVo对象", description = "签约明细审核日志表V3.0")
public class BbsiRuleResultLogInfoVo extends BasePageResultVo implements Serializable {


    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @NotBlank(message = "主键不能为空", groups = {UpdateValidatorGroup.class})
    private String ruleLogId;

    /**
     * 原因
     */
    @ApiModelProperty(value = "原因")
    private String reason;

    /**
     * 审核状态
     */
    @ApiModelProperty(value = "审核状态")
    private String anditStatus;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
    private Integer delFlag;

    /**
     * 签约明细id
     */
    @ApiModelProperty(value = "签约明细id")
    private String signRuleResultId;


    /**
     * @return 主键
     */
    public String getRuleLogId() {
        return ruleLogId;
    }

    public void setRuleLogId(String ruleLogId) {
        this.ruleLogId = ruleLogId;
    }

    /**
     * @return 原因
     */
    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    /**
     * @return 审核状态
     */
    public String getAnditStatus() {
        return anditStatus;
    }

    public void setAnditStatus(String anditStatus) {
        this.anditStatus = anditStatus;
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * @return 签约明细id
     */
    public String getSignRuleResultId() {
        return signRuleResultId;
    }

    public void setSignRuleResultId(String signRuleResultId) {
        this.signRuleResultId = signRuleResultId;
    }

    @Override
    public String toString() {
        return "BbsiRuleResultLogInfoVo{" +
                "ruleLogId=" + ruleLogId +
                ", reason=" + reason +
                ", anditStatus=" + anditStatus +
                ", delFlag=" + delFlag +
                ", signRuleResultId=" + signRuleResultId +
                "}";
    }
}
