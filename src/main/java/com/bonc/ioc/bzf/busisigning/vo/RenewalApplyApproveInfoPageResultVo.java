package com.bonc.ioc.bzf.busisigning.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 续签申请信息分页列表 分页查询返回的结果
 *
 * @author: he<PERSON>ya<PERSON>
 * @createDate: 2023-09-21
 * @Version 1.0
 **/
@Data
@ApiModel(value = "RenewalApplyApproveInfoPageResultVo", description = "续签申请信息分页列表")
public class RenewalApplyApproveInfoPageResultVo {

    /**
     * 申请信息id
     */
    @ApiModelProperty(value = "申请信息id")
    private String renewalApplyInfoId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    /**
     * 小区名称
     */
    @ApiModelProperty(value = "小区名称")
    private String communityName;


    /**
     * 商铺地址
     */
    @ApiModelProperty(value = "商铺地址")
    private String productName;

    /**
     * 租户名称
     */
    @ApiModelProperty(value = "租户名称")
    private String tenantName;

    /**
     * 租户性质
     */
    @ApiModelProperty(value = "租户性质")
    private String customerType;

    /**
     * 租户性质名称
     */
    @ApiModelProperty(value = "租户性质名称")
    private String customerTypeName;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String customerTel;

    /**
     * 业态
     */
    @ApiModelProperty(value = "业态")
    private String businessFormat;

    /**
     * 业态名称
     */
    @ApiModelProperty(value = "业态名称")
    private String businessFormatName;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String submitUserTel;

    /**
     * 申请说明
     */
    @ApiModelProperty(value = "申请说明")
    private String submitIllustrate;

    /**
     * 审批id
     */
    @ApiModelProperty(value = "审批id")
    private String approveId;

    /**
     * 上级id（合同编码）
     */
    @ApiModelProperty(value = "上级id（申请信息id）")
    private String parentId;

    /**
     * 意见说明
     */
    @ApiModelProperty(value = "意见说明")
    private String commentExplanation;

    /**
     * 审批状态(1.通过 2.未通过 3.待审核 4.撤回)
     */
    @ApiModelProperty(value = "审批状态(1.通过 2.未通过 3.待审核 4.撤回)")
    private String approveStatus;

    /**
     * 提交时间
     */
    @ApiModelProperty(value = "提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date submitTime;

    /**
     * 审批时间
     */
    @ApiModelProperty(value = "审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approveTime;

    /**
     * 提交人id
     */
    @ApiModelProperty(value = "提交人id")
    private String submitUserId;

    /**
     * 提交人姓名
     */
    @ApiModelProperty(value = "提交人姓名")
    private String submitUserName;

    /**
     * 审批人id
     */
    @ApiModelProperty(value = "审批人id")
    private String approverUserId;

    /**
     * 审批人姓名
     */
    @ApiModelProperty(value = "审批人姓名")
    private String approverUserName;

    /**
     * 删除标识(1.未删除 0.已删除)
     */
    @ApiModelProperty(value = "删除标识(1.未删除 0.已删除)")
    private Integer delFlag;

}
