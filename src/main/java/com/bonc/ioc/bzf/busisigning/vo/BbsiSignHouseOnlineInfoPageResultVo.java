package com.bonc.ioc.bzf.busisigning.vo;


import com.bonc.ioc.bzf.utils.common.convert.CopyFieldPoint;
import com.bonc.ioc.bzf.utils.common.convert.CopyFieldUtil;
import com.bonc.ioc.common.base.vo.McpBasePageVo;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 在线选房结果信息表V3.0 实体类
 *
 * <AUTHOR>
 * @date 2022-11-30
 * @change 2022-11-30 by ly for init
 */
@ApiModel(value = "BbsiSignHouseOnlineInfoPageResultVo对象", description = "在线选房结果信息表V3.0")
public class BbsiSignHouseOnlineInfoPageResultVo extends McpBasePageVo implements Serializable {


    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @NotBlank(message = "主键不能为空", groups = {UpdateValidatorGroup.class})
    private String relaId;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String customName;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private String customId;


    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id2")
    private String customerId;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private String customGender;

    /**
     * 证件类型
     */
    @ApiModelProperty(value = "证件类型")
    private String certificateType;


    @ApiModelProperty(value = "性别")
    private String customGenderText;


    /**
     * 证件类型
     */
    @ApiModelProperty(value = "证件类型")
    private String certificateTypeText;

    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    private String certificateNum;

    /**
     * 楼号
     */
    @ApiModelProperty(value = "楼号")
    private String buildingNo;
    /**
     * 楼名称
     */
    @ApiModelProperty(value = "楼名称")
    private String buildingName;
    /**
     * 单元名称
     */
    @ApiModelProperty(value = "单元名称")
    private String unitName;
    /**
     * 单元
     */
    @ApiModelProperty(value = "单元")
    private String unitNo;

    /**
     * 房间号
     */
    @ApiModelProperty(value = "房间号")
    private String roomNo;

    /**
     * 房屋地址
     */
    @ApiModelProperty(value = "房屋地址")
    private String houseSource;

    /**
     * 项目名称小区名称
     */
    @ApiModelProperty(value = "项目名称小区名称")
    @CopyFieldPoint(fieldName = "infoName", type = CopyFieldUtil.TYPE_PROJECT)
    private String projectName;

    /**
     * 项目ID(小区ID)
     */
    @ApiModelProperty(value = "项目ID(小区ID)")
    private String communityId;

    /**
     * 计划ID
     */
    @ApiModelProperty(value = "计划ID")
    private String planId;

    /**
     * 房态中心房源id
     */
    @ApiModelProperty(value = "房态中心房源id")
    private String assetId;

    /**
     * 朝向
     */
    @ApiModelProperty(value = "朝向")
    private String houseToward;

    /**
     * 单价
     */
    @ApiModelProperty(value = "单价")
    private String price;

    /**
     * 面积
     */
    @ApiModelProperty(value = "面积")
    private String houseUsableArea;

    /**
     * 楼层
     */
    @ApiModelProperty(value = "楼层")
    private String houseFloor;

    /**
     * 户型
     */
    @ApiModelProperty(value = "户型")
    private String houseType;

    /**
     * 选房时间
     */
    @ApiModelProperty(value = "选房时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date selTime;

    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String mobile;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
    private Integer delFlag;

    /**
     * 组织机构id
     */
    @ApiModelProperty(value = "组织机构id")
    private String orgId;

    /**
     * 出租人(单位)名称
     */
    @ApiModelProperty(value = "出租人(单位)名称")
    private String lessorName;

    /**
     * 统一信用代码
     */
    @ApiModelProperty(value = "统一信用代码")
    private String unifiedCreditCode;

    /**
     * 项目(小区)服务站联系电话
     */
    @ApiModelProperty(value = "项目(小区)服务站联系电话")
    private String firstPartyPhone;

    /**
     * 甲方通讯地址
     */
    @ApiModelProperty(value = "甲方通讯地址")
    private String firstPartyAddress;

    /**
     * 甲方邮编
     */
    @ApiModelProperty(value = "甲方邮编")
    private String firstPartyPostalCode;

    /**
     * 乙方备案号
     */
    @ApiModelProperty(value = "乙方备案号")
    private String secondPartyFilingNo;

    /**
     * 乙方固定电话
     */
    @ApiModelProperty(value = "乙方固定电话")
    private String secondPartyFixedTelephone;

    /**
     * 乙方工作单位
     */
    @ApiModelProperty(value = "乙方工作单位")
    private String secondPartyWorkUnit;
    /**
     * 乙方通讯地址
     */
    @ApiModelProperty(value = "乙方通讯地址")
    private String secondPartyAddress;
    /**
     * 乙方邮编
     */
    @ApiModelProperty(value = "乙方邮编")
    private String secondPartyPostalCode;

    /**
     * 房屋所在区
     */
    @ApiModelProperty(value = "房屋所在区")
    private String houseLocation;

    /**
     * 房屋街道(路)
     */
    @ApiModelProperty(value = "房屋街道(路)")
    private String houseStreet;

    /**
     * 由乙方承担的费用项
     */
    @ApiModelProperty(value = "由乙方承担的费用项")
    private String secondPartyCost;

    /**
     * 合同总份数
     */
    @ApiModelProperty(value = "合同总份数")
    private String totalContractNum;

    /**
     * 甲方持合同份数
     */
    @ApiModelProperty(value = "甲方持合同份数")
    private String firstPartyContractNum;

    /**
     * 已方持合同份数
     */
    @ApiModelProperty(value = "已方持合同份数")
    private String secondPartyContractNum;

    /**
     * 委托人姓名
     */
    @ApiModelProperty(value = "委托人姓名")
    private String clientName;

    /**
     * 委托人联系电话
     */
    @ApiModelProperty(value = "委托人联系电话")
    private String clientTelephone;

    /**
     * 小区经度
     */
    @ApiModelProperty(value = "小区经度")
    private String houseLongitude;

    /**
     * 小区纬度
     */
    @ApiModelProperty(value = "小区纬度")
    private String houseLatitude;


    @ApiModelProperty(value = "配租计划id")
    private String peizuPlanId;


    @ApiModelProperty(value = "配租计划名称")
    private String peizuPlanName;


    @ApiModelProperty(value = "当前人数")
    private String num;


    @ApiModelProperty(value = "签约计划id")
    private String signRuleId;

    @ApiModelProperty(value = "租金标准")
    private String rentStandard;


    /**
     * 房屋地址
     */
    @ApiModelProperty(value = "房屋地址2-前端特殊处理使用")
    private String houseResource;

    /**
     * 房屋地址
     */
    @ApiModelProperty(value = "房屋编号2-前端特殊处理使用")
    private String houseResourceId;

    @ApiModelProperty(value = "项目ID")
    private String projectId;

    @ApiModelProperty(value = "人房关系id")
    private String relationId;


    public String getBuildingName() {
        return buildingName;
    }

    public void setBuildingName(String buildingName) {
        this.buildingName = buildingName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getRelationId() {
        return relationId;
    }

    public void setRelationId(String relationId) {
        this.relationId = relationId;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }


    public String getHouseResource() {
        return houseResource;
    }

    public void setHouseResource(String houseResource) {
        this.houseResource = houseResource;
    }

    public String getHouseResourceId() {
        return houseResourceId;
    }

    public void setHouseResourceId(String houseResourceId) {
        this.houseResourceId = houseResourceId;
    }

    public String getRentStandard() {
        return rentStandard;
    }

    public void setRentStandard(String rentStandard) {
        this.rentStandard = rentStandard;
    }


    public String getSignRuleId() {
        return signRuleId;
    }

    public void setSignRuleId(String signRuleId) {
        this.signRuleId = signRuleId;
    }

    public String getNum() {
        return num;
    }

    public void setNum(String num) {
        this.num = num;
    }

    public String getPeizuPlanId() {
        return peizuPlanId;
    }

    public void setPeizuPlanId(String peizuPlanId) {
        this.peizuPlanId = peizuPlanId;
    }

    public String getPeizuPlanName() {
        return peizuPlanName;
    }

    public void setPeizuPlanName(String peizuPlanName) {
        this.peizuPlanName = peizuPlanName;
    }

    public String getLessorName() {
        return lessorName;
    }

    public void setLessorName(String lessorName) {
        this.lessorName = lessorName;
    }

    public String getUnifiedCreditCode() {
        return unifiedCreditCode;
    }

    public void setUnifiedCreditCode(String unifiedCreditCode) {
        this.unifiedCreditCode = unifiedCreditCode;
    }

    public String getFirstPartyPhone() {
        return firstPartyPhone;
    }

    public void setFirstPartyPhone(String firstPartyPhone) {
        this.firstPartyPhone = firstPartyPhone;
    }

    public String getFirstPartyAddress() {
        return firstPartyAddress;
    }

    public void setFirstPartyAddress(String firstPartyAddress) {
        this.firstPartyAddress = firstPartyAddress;
    }

    public String getFirstPartyPostalCode() {
        return firstPartyPostalCode;
    }

    public void setFirstPartyPostalCode(String firstPartyPostalCode) {
        this.firstPartyPostalCode = firstPartyPostalCode;
    }

    public String getSecondPartyFilingNo() {
        return secondPartyFilingNo;
    }

    public void setSecondPartyFilingNo(String secondPartyFilingNo) {
        this.secondPartyFilingNo = secondPartyFilingNo;
    }

    public String getSecondPartyFixedTelephone() {
        return secondPartyFixedTelephone;
    }

    public void setSecondPartyFixedTelephone(String secondPartyFixedTelephone) {
        this.secondPartyFixedTelephone = secondPartyFixedTelephone;
    }

    public String getSecondPartyWorkUnit() {
        return secondPartyWorkUnit;
    }

    public void setSecondPartyWorkUnit(String secondPartyWorkUnit) {
        this.secondPartyWorkUnit = secondPartyWorkUnit;
    }

    public String getSecondPartyPostalCode() {
        return secondPartyPostalCode;
    }

    public void setSecondPartyPostalCode(String secondPartyPostalCode) {
        this.secondPartyPostalCode = secondPartyPostalCode;
    }

    public String getHouseLocation() {
        return houseLocation;
    }

    public void setHouseLocation(String houseLocation) {
        this.houseLocation = houseLocation;
    }

    public String getHouseStreet() {
        return houseStreet;
    }

    public void setHouseStreet(String houseStreet) {
        this.houseStreet = houseStreet;
    }

    public String getSecondPartyCost() {
        return secondPartyCost;
    }

    public void setSecondPartyCost(String secondPartyCost) {
        this.secondPartyCost = secondPartyCost;
    }

    public String getTotalContractNum() {
        return totalContractNum;
    }

    public void setTotalContractNum(String totalContractNum) {
        this.totalContractNum = totalContractNum;
    }

    public String getFirstPartyContractNum() {
        return firstPartyContractNum;
    }

    public void setFirstPartyContractNum(String firstPartyContractNum) {
        this.firstPartyContractNum = firstPartyContractNum;
    }

    public String getSecondPartyContractNum() {
        return secondPartyContractNum;
    }

    public void setSecondPartyContractNum(String secondPartyContractNum) {
        this.secondPartyContractNum = secondPartyContractNum;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public String getClientTelephone() {
        return clientTelephone;
    }

    public void setClientTelephone(String clientTelephone) {
        this.clientTelephone = clientTelephone;
    }

    public String getHouseLongitude() {
        return houseLongitude;
    }

    public void setHouseLongitude(String houseLongitude) {
        this.houseLongitude = houseLongitude;
    }

    public String getHouseLatitude() {
        return houseLatitude;
    }

    public void setHouseLatitude(String houseLatitude) {
        this.houseLatitude = houseLatitude;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    /**
     * @return 主键
     */
    public String getRelaId() {
        return relaId;
    }

    public void setRelaId(String relaId) {
        this.relaId = relaId;
    }

    /**
     * @return 姓名
     */
    public String getCustomName() {
        return customName;
    }

    public void setCustomName(String customName) {
        this.customName = customName;
    }

    /**
     * @return 客户id
     */
    public String getCustomId() {
        return customId;
    }

    public void setCustomId(String customId) {
        this.customId = customId;
    }

    /**
     * @return 性别
     */
    public String getCustomGender() {
        return customGender;
    }

    public void setCustomGender(String customGender) {
        this.customGender = customGender;
    }

    /**
     * @return 证件类型
     */
    public String getCertificateType() {
        return certificateType;
    }

    public void setCertificateType(String certificateType) {
        this.certificateType = certificateType;
    }

    /**
     * @return 身份证号
     */
    public String getCertificateNum() {
        return certificateNum;
    }

    public void setCertificateNum(String certificateNum) {
        this.certificateNum = certificateNum;
    }

    /**
     * @return 楼号
     */
    public String getBuildingNo() {
        return buildingNo;
    }

    public void setBuildingNo(String buildingNo) {
        this.buildingNo = buildingNo;
    }

    /**
     * @return 单元
     */
    public String getUnitNo() {
        return unitNo;
    }

    public void setUnitNo(String unitNo) {
        this.unitNo = unitNo;
    }

    /**
     * @return 房间号
     */
    public String getRoomNo() {
        return roomNo;
    }

    public void setRoomNo(String roomNo) {
        this.roomNo = roomNo;
    }

    /**
     * @return 房屋地址
     */
    public String getHouseSource() {
        return houseSource;
    }

    public void setHouseSource(String houseSource) {
        this.houseSource = houseSource;
    }

    /**
     * @return 项目名称小区名称
     */
    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    /**
     * @return 项目ID(小区ID)
     */
    public String getCommunityId() {
        return communityId;
    }

    public void setCommunityId(String communityId) {
        this.communityId = communityId;
    }

    /**
     * @return 计划ID
     */
    public String getPlanId() {
        return planId;
    }

    public void setPlanId(String planId) {
        this.planId = planId;
    }

    /**
     * @return 房态中心房源id
     */
    public String getAssetId() {
        return assetId;
    }

    public void setAssetId(String assetId) {
        this.assetId = assetId;
    }

    /**
     * @return 朝向
     */
    public String getHouseToward() {
        return houseToward;
    }

    public void setHouseToward(String houseToward) {
        this.houseToward = houseToward;
    }

    /**
     * @return 单价
     */
    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    /**
     * @return 面积
     */
    public String getHouseUsableArea() {
        return houseUsableArea;
    }

    public void setHouseUsableArea(String houseUsableArea) {
        this.houseUsableArea = houseUsableArea;
    }

    /**
     * @return 楼层
     */
    public String getHouseFloor() {
        return houseFloor;
    }

    public void setHouseFloor(String houseFloor) {
        this.houseFloor = houseFloor;
    }

    /**
     * @return 户型
     */
    public String getHouseType() {
        return houseType;
    }

    public void setHouseType(String houseType) {
        this.houseType = houseType;
    }

    /**
     * @return 选房时间
     */
    public Date getSelTime() {
        if (selTime != null) {
            return (Date) selTime.clone();
        } else {
            return null;
        }
    }

    public void setSelTime(Date selTime) {
        if (selTime == null) {
            this.selTime = null;
        } else {
            this.selTime = (Date) selTime.clone();
        }
    }

    /**
     * @return 手机号
     */
    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public String getSecondPartyAddress() {
        return secondPartyAddress;
    }

    public void setSecondPartyAddress(String secondPartyAddress) {
        this.secondPartyAddress = secondPartyAddress;
    }


    public String getCustomGenderText() {
        return customGenderText;
    }

    public void setCustomGenderText(String customGenderText) {
        this.customGenderText = customGenderText;
    }

    public String getCertificateTypeText() {
        return certificateTypeText;
    }

    public void setCertificateTypeText(String certificateTypeText) {
        this.certificateTypeText = certificateTypeText;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    @Override
    public String toString() {
        return "BbsiSignHouseOnlineInfoPageResultVo{" +
                "relaId=" + relaId +
                ", customName=" + customName +
                ", customId=" + customId +
                ", customGender=" + customGender +
                ", certificateType=" + certificateType +
                ", certificateNum=" + certificateNum +
                ", buildingNo=" + buildingNo +
                ", unitNo=" + unitNo +
                ", roomNo=" + roomNo +
                ", houseSource=" + houseSource +
                ", projectName=" + projectName +
                ", communityId=" + communityId +
                ", planId=" + planId +
                ", assetId=" + assetId +
                ", houseToward=" + houseToward +
                ", price=" + price +
                ", houseUsableArea=" + houseUsableArea +
                ", houseFloor=" + houseFloor +
                ", houseType=" + houseType +
                ", selTime=" + selTime +
                ", mobile=" + mobile +
                ", delFlag=" + delFlag +
                "}";
    }
}
