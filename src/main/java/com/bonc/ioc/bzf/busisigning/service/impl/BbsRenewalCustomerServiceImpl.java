package com.bonc.ioc.bzf.busisigning.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.bonc.ioc.bzf.busisigning.entity.BbsRenewalCustomerEntity;
import com.bonc.ioc.bzf.busisigning.dao.BbsRenewalCustomerMapper;
import com.bonc.ioc.bzf.busisigning.enums.DelFlagEnum;
import com.bonc.ioc.bzf.busisigning.service.IBbsRenewalCustomerService;
import com.bonc.ioc.common.base.service.impl.McpBaseServiceImpl;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.bonc.ioc.common.exception.McpException;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import java.util.Collections;
import org.apache.commons.lang3.StringUtils;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import com.bonc.ioc.bzf.busisigning.vo.*;
import org.springframework.beans.BeanUtils;
import java.util.ArrayList;
import com.bonc.ioc.common.base.page.PageResult;

/**
 * 人-产品：客户表 服务类实现
 *
 * <AUTHOR>
 * @date 2023-09-22
 * @change 2023-09-22 by King-Y for init
 */
@Slf4j
@Service
public class BbsRenewalCustomerServiceImpl extends McpBaseServiceImpl<BbsRenewalCustomerEntity> implements IBbsRenewalCustomerService {
    /**
     * mapper 访问数据库
     */
    @Resource
    private BbsRenewalCustomerMapper baseMapper;

    /**
     * service 本服务
     */
    @Resource
    private IBbsRenewalCustomerService baseService;

    /**
     * insertRecord 新增
     * @param vo 需要保存的记录
     * @return  String 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
     @Override
     @Transactional(rollbackFor = {Exception.class})
     public String insertRecord(BbsRenewalCustomerVo vo) {
        if(vo == null) {
            return null;
        }

        BbsRenewalCustomerEntity entity = new BbsRenewalCustomerEntity();
        BeanUtils.copyProperties(vo, entity);

        entity.setRcId(null);
        if(!baseService.insert(entity)) {
            log.error("人-产品：客户表新增失败:" + entity.toString());
            throw new McpException("人-产品：客户表新增失败");
        }else {
            if(!baseService.saveOperationHisById(entity.getRcId(),1)) {
                log.error("人-产品：客户表新增后保存历史失败:" + entity.toString());
                throw new McpException("人-产品：客户表新增后保存历史失败");
            }

            log.debug("人-产品：客户表新增成功:"+entity.getRcId());
            return entity.getRcId();
        }
     }

    /**
     * insertBatchRecord 批量新增
     * @param voList 需要保存的记录
     * @return  List<String> 返回新增后的主键
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public List<String> insertBatchRecord(List<BbsRenewalCustomerVo> voList) {
        if(CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }

        List<BbsRenewalCustomerEntity> entityList = new ArrayList<>();
        for (BbsRenewalCustomerVo item:voList) {
            BbsRenewalCustomerEntity entity = new BbsRenewalCustomerEntity();
            BeanUtils.copyProperties(item, entity);
            entityList.add(entity);
        }

        for (BbsRenewalCustomerEntity item:entityList){
            item.setRcId(null);
        }

        if(!baseService.insertBatch(entityList)) {
            log.error("人-产品：客户表新增失败");
            throw new McpException("人-产品：客户表新增失败");
        }else{
            List<String> kidList = entityList.stream().map(BbsRenewalCustomerEntity::getRcId).collect(Collectors.toList());

            if(!baseService.saveOperationHisByIds(kidList,1)) {
                log.error("人-产品：客户表批量新增后保存历史失败:" + StringUtils.join(kidList));
                throw new McpException("人-产品：客户表批量新增后保存历史失败");
            }

            log.debug("人-产品：客户表新增成功:"+ StringUtils.join(kidList));
            return kidList;
        }
    }

    /**
     * removeByIdRecord 根据主键删除
     * @param rcId 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdRecord(String rcId) {
        if(!StringUtils.isEmpty(rcId)) {
            if(!baseService.saveOperationHisById(rcId,3)) {
                log.error("人-产品：客户表删除后保存历史失败:" + rcId);
                throw new McpException("人-产品：客户表删除后保存历史失败");
            }

            if(!baseService.removeById(rcId)) {
                log.error("人-产品：客户表删除失败");
                throw new McpException("人-产品：客户表删除失败"+rcId);
            }
        } else {
            throw new McpException("人-产品：客户表删除失败主键为空");
        }
    }

    /**
     * removeByIdsRecord 根据主键集合删除
     * @param rcIdList 需要删除的主键
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void removeByIdsRecord(List<String> rcIdList) {
        if(!CollectionUtils.isEmpty(rcIdList)) {
            int oldSize = rcIdList.size();
            rcIdList = rcIdList.stream().filter(t->StringUtils.isNotBlank(t)).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(rcIdList) || oldSize != rcIdList.size()) {
                throw new McpException("人-产品：客户表批量删除失败 存在主键id为空的记录"+StringUtils.join(rcIdList));
            }

            if(!baseService.saveOperationHisByIds(rcIdList,3)) {
                log.error("人-产品：客户表批量删除后保存历史失败:" + StringUtils.join(rcIdList));
                throw new McpException("人-产品：客户表批量删除后保存历史失败");
            }

            if(!baseService.removeByIds(rcIdList)) {
                log.error("人-产品：客户表批量删除失败");
                throw new McpException("人-产品：客户表批量删除失败"+StringUtils.join(rcIdList));
            }
        }
    }

    /**
     * updateByIdRecord 根据主键更新 表全部信息
     * @param vo 需要更新的人-产品：客户表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateByIdRecord(BbsRenewalCustomerVo vo) {
        if(vo != null) {
            BbsRenewalCustomerEntity entity = new BbsRenewalCustomerEntity();
            BeanUtils.copyProperties(vo, entity);

            if(StringUtils.isEmpty(entity.getRcId())) {
                throw new McpException("人-产品：客户表更新失败传入主键为空");
            }

            if(!baseService.updateById(entity)) {
                log.error("人-产品：客户表更新失败");
                throw new McpException("人-产品：客户表更新失败"+entity.getRcId());
            } else {
                if(!baseService.saveOperationHisById(entity.getRcId(),2)) {
                    log.error("人-产品：客户表更新后保存历史失败:" + entity.getRcId());
                    throw new McpException("人-产品：客户表更新后保存历史失败");
                }
            }
        } else {
            throw new McpException("人-产品：客户表更新失败传入为空");
        }
    }

    /**
     * updateBatchByIdRecord 根据主键集合更新
     * @param voList 需要更新的人-产品：客户表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void updateBatchByIdRecord(List<BbsRenewalCustomerVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbsRenewalCustomerEntity> entityList = new ArrayList<>();

            for (BbsRenewalCustomerVo item:voList){
                BbsRenewalCustomerEntity entity = new BbsRenewalCustomerEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            int oldSize = entityList.size();
            entityList = entityList.stream().filter(t->StringUtils.isNotBlank(t.getRcId())).collect(Collectors.toList());

            if(CollectionUtils.isEmpty(entityList) || oldSize != entityList.size()) {
                throw new McpException("人-产品：客户表批量更新失败 存在主键为空的记录");
            }

            if(!baseService.updateBatchById(entityList)) {
                log.error("人-产品：客户表批量更新失败");
                throw new McpException("人-产品：客户表批量更新失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getRcId())).map(BbsRenewalCustomerEntity::getRcId).collect(Collectors.toList());
                if(!baseService.saveOperationHisByIds(kidList,2)) {
                    log.error("人-产品：客户表批量更新后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("人-产品：客户表批量更新后保存历史失败");
                }
            }
        }
    }

    /**
     * saveByIdRecord 根据主键更新或新增
     * @param vo 需要更新的人-产品：客户表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveByIdRecord(BbsRenewalCustomerVo vo) {
        if(vo != null) {
            BbsRenewalCustomerEntity entity = new BbsRenewalCustomerEntity();
            BeanUtils.copyProperties(vo, entity);

            if(!baseService.saveById(entity)) {
                log.error("人-产品：客户表保存失败");
                throw new McpException("人-产品：客户表保存失败"+entity.getRcId());
            } else {
                if(!baseService.saveOperationHisById(entity.getRcId(),4)) {
                    log.error("人-产品：客户表保存后保存历史失败:" + entity.getRcId());
                    throw new McpException("人-产品：客户表保存后保存历史失败");
                }
            }
        } else {
            throw new McpException("人-产品：客户表保存失败传入为空");
        }
    }

    /**
     * saveBatchByIdRecord 根据主键更新或新增
     * @param voList 需要删除的人-产品：客户表
     * @return  void
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveBatchByIdRecord(List<BbsRenewalCustomerVo> voList) {
        if(!CollectionUtils.isEmpty(voList)) {
            List<BbsRenewalCustomerEntity> entityList = new ArrayList<>();

            for (BbsRenewalCustomerVo item:voList){
                BbsRenewalCustomerEntity entity = new BbsRenewalCustomerEntity();
                BeanUtils.copyProperties(item, entity);
                entityList.add(entity);
            }

            if(!baseService.saveBatchById(entityList)) {
                log.error("人-产品：客户表批量保存失败");
                throw new McpException("人-产品：客户表批量保存失败");
            } else {
                List<String> kidList = entityList.stream().filter(t-> StringUtils.isNotBlank(t.getRcId())).map(BbsRenewalCustomerEntity::getRcId).collect(Collectors.toList());

                if(!baseService.saveOperationHisByIds(kidList,4)) {
                    log.error("人-产品：客户表批量保存后保存历史失败:" + StringUtils.join(kidList));
                    throw new McpException("人-产品：客户表批量保存后保存历史失败");
                }
            }
        }
    }

    /**
     * selectByIdRecord 根据主键查询
     * @param rcId 需要查询的主键
     * @return  根据id查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public BbsRenewalCustomerVo selectByIdRecord(String rcId) {
        BbsRenewalCustomerVo vo = new BbsRenewalCustomerVo();

        if(!StringUtils.isEmpty(rcId)) {
            BbsRenewalCustomerEntity entity = baseService.selectById(rcId);

            if(entity != null) {
                BeanUtils.copyProperties(entity, vo);
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * selectByPageRecord 分页查询
     * @param vo 需要查询的条件
     * @return  分页查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-09-22
     * @change
     * 2023-09-22 by King-Y for init
     */
    @Override
    @Transactional(rollbackFor = {Exception.class})
    public PageResult<List<BbsRenewalCustomerPageResultVo>> selectByPageRecord(BbsRenewalCustomerPageVo vo) {
        List<BbsRenewalCustomerPageResultVo> result = baseMapper.selectByPageCustom(vo);
        return new PageResult(result);
    }

    /**
     * 根据人房关系id查询
     *
     * @param rrId 人房关系id
     * @return 客户信息 vo实体
     */
    @Override
    public BbsRenewalCustomerVo selectByRrId(String rrId) {
        BbsRenewalCustomerEntity entity = new LambdaQueryChainWrapper<>(baseMapper)
                .eq(BbsRenewalCustomerEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsRenewalCustomerEntity::getRrId, rrId)
                .one();
        if (Objects.isNull(entity)) {
            return null;
        }
        BbsRenewalCustomerVo vo = new BbsRenewalCustomerVo();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }
}
