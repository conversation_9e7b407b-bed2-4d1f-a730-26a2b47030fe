package com.bonc.ioc.bzf.busisigning.feign.feign;

import com.bonc.ioc.bzf.busisigning.feign.vo.BbsCheckChangeVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.CheckInSignDataVo;
import com.bonc.ioc.bzf.utils.common.log.LogPoint;
import com.bonc.ioc.common.aop.FeignExceptionCheck;
import com.bonc.ioc.common.config.FeignExceptionConfiguration;
import com.bonc.ioc.common.util.AppReply;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(value = "bzf-system-commerce-checkin", fallback = FeignExceptionConfiguration.class,
        configuration = FeignExceptionConfiguration.class)
public interface BbcCheckInV2FeignClient {
    /**
     * 签约推送入住数据接口
     * @param BbciCustCheckInVo
     * @return
     */
    @PostMapping(value = "/bzf-system-commerce-checkin/commerceCheckinManage/batch/insertRecord", produces = "application/json;charset=UTF-8")
    @FeignExceptionCheck
    @LogPoint(system = "bzf-system-commerce-checkin")
    AppReply insertRecord(@RequestBody List<CheckInSignDataVo> BbciCustCheckInVo);


    /**
     * 根据合同编号查询入住信息
     * @param contractCode
     * @param customerId
     * @return
     */
    @GetMapping(value = "/bzf-system-commerce-checkin/commerceCheckinManage/checkin/details/contractcode", produces = "application/json;charset=UTF-8")
    @FeignExceptionCheck
    @LogPoint(system = "bzf-system-commerce-checkin")
    AppReply getCheckinInfoByContractCode(@RequestParam(value = "contractCode") String contractCode,@RequestParam(value = "customerId") String customerId);

    @PostMapping(value = "/bzf-system-commerce-checkin/commerceCheckinManage/checkin/change/contractcode", produces = "application/json;charset=UTF-8")
    @FeignExceptionCheck
    @LogPoint(system = "bzf-system-commerce-checkin")
    AppReply contractcode(@RequestBody BbsCheckChangeVo checkChangeVo);
}
