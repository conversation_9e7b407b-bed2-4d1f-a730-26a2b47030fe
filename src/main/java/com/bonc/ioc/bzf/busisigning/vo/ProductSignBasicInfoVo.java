package com.bonc.ioc.bzf.busisigning.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> liu<PERSON>g<PERSON>
 * @date : 2023/9/12
 */
@Data
public class ProductSignBasicInfoVo {
    //签约业态名称
    @ApiModelProperty(value = "业态名称")
    private String businessFormatName;
    // 月租金总额（元/月）
    @ApiModelProperty(value = "月租金总额")
    private String monthlyRent;
    // 租金标准
    @ApiModelProperty(value = "租金标准")
    private String rentStandard;
    // 租金标准单位名称
    @ApiModelProperty(value = "租金标准单位名称")
    private String rentStandardUnitName;
    // 租户性质名称
    @ApiModelProperty(value = "租户性质名称")
    private String customerTypeName;
    // 租户名称
    @ApiModelProperty(value = "租户名称")
    private String customerName;
    // 合同日期开始日期（yyyy-MM-dd）
    @ApiModelProperty(value = "合同日期开始日期（yyyy-MM-dd）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractBeginTime;
    // 合同日期结束日期（yyyy-MM-dd）
    @ApiModelProperty(value = "合同日期结束日期（yyyy-MM-dd）")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractEndTime;
    // 缴费周期名称
    @ApiModelProperty(value = "缴费周期名称")
    private String paymentCycleName;
    @ApiModelProperty(value = "月租金规则编号", hidden = true)
    @JsonIgnore
    private String monthlyRentRulesNo;
    @ApiModelProperty(value = "面积", hidden = true)
    @JsonIgnore
    private String houseStructArea;
    @ApiModelProperty(value = "楼层", hidden = true)
    @JsonIgnore
    private String currentFloorNo;
    @ApiModelProperty(value = "户型", hidden = true)
    @JsonIgnore
    private String houseTypeNo;
    @ApiModelProperty(value = "朝向", hidden = true)
    @JsonIgnore
    private String houseOrientation;
}
