package com.bonc.ioc.bzf.busisigning.enums;

/**
 * 缴费的退回类型 枚举类
 *
 * <AUTHOR>
 * @since 2024/10/31
 */
public enum PaymentBackTypeEnum {

    /**
     * 原路
     */
    BACK_TRACK("01", "原路"),

    /**
     * 非原路
     */
    NON_BACK_TRACK("02", "非原路");

    /**
     * 编号
     */
    private String code;

    /**
     * 描述信息
     */
    private String desc;

    /**
     * 获取编号
     *
     * @return 编号
     */
    public String getCode() {
        return this.code;
    }

    /**
     * 获取描述信息
     *
     * @return 描述信息
     */
    public String getDesc() {
        return this.desc;
    }

    /**
     * 构造方法
     *
     * @param code 编号
     * @param desc 描述信息
     */
    PaymentBackTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 构造方法
     */
    PaymentBackTypeEnum() {
    }

    /**
     * 根据编号查询描述信息
     *
     * @param code 编号
     * @return 描述信息
     */
    public static String getDescByCode(String code) {
        PaymentBackTypeEnum[] enums = values();
        for (PaymentBackTypeEnum en : enums) {
            if (en.getCode().equals(code)) {
                return en.getDesc();
            }
        }
        return null;
    }
}
