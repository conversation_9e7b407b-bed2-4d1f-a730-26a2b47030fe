package com.bonc.ioc.bzf.busisigning.feign.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 存储流水号和凭证
 *
 * <AUTHOR>
 * @date 2022/12/16 15:34
 * @change 2022/12/16 15:34 by l<PERSON><PERSON><PERSON> for init
 */
@Data
public class BankMistnoVo {
    @ApiModelProperty(value = "凭证")
    private String pospbak;
    @ApiModelProperty(value = "MIS流水号")
    private String mistno;
}
