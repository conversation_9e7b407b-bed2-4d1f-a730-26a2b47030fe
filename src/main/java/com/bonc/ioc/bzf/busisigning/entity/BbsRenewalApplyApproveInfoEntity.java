package com.bonc.ioc.bzf.busisigning.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bonc.ioc.common.base.entity.McpBaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * 续签申请信息表
 *
 * @author: hechengyao
 * @createDate: 2023-09-21
 * @Version 1.0
 **/
@TableName("bbs_renewal_apply_approve_info")
@ApiModel(value = "RenewalApproveInfoEntity", description = "续签申请信息表")
public class BbsRenewalApplyApproveInfoEntity extends McpBaseEntity implements Serializable {

    /**
     * 审批id
     */
    @ApiModelProperty(value = "审批id")
    @TableId(value = "approve_id", type = IdType.ASSIGN_UUID)
    private String approveId;

    /**
     * 上级id（合同编码）
     */
    @ApiModelProperty(value = "上级id（合同编码）")
    private String parentId;

    /**
     * 意见说明
     */
    @ApiModelProperty(value = "意见说明")
    private String commentExplanation;

    /**
     * 审批状态(1.通过 2.未通过 3.待审核 4.撤回)
     */
    @ApiModelProperty(value = "审批状态(1.通过 2.未通过 3.待审核 4.撤回)")
    private String approveStatus;

    /**
     * 提交时间
     */
    @ApiModelProperty(value = "提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date submitTime;

    /**
     * 审批时间
     */
    @ApiModelProperty(value = "审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approveTime;

    /**
     * 提交人id
     */
    @ApiModelProperty(value = "提交人id")
    private String submitUserId;

    /**
     * 审批人id
     */
    @ApiModelProperty(value = "审批人id")
    private String approverUserId;

    /**
     * 删除标识(1.未删除 0.已删除)
     */
    @ApiModelProperty(value = "删除标识(1.未删除 0.已删除)")
    private Integer delFlag;

    /**
     * 提交人姓名
     */
    @ApiModelProperty(value = "提交人姓名")
    private String submitUserName;

    /**
     * 审批人姓名
     */
    @ApiModelProperty(value = "审批人姓名")
    private String approverUserName;

    public String getApproveId() {
        return approveId;
    }

    public void setApproveId(String approveId) {
        this.approveId = approveId;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getCommentExplanation() {
        return commentExplanation;
    }

    public void setCommentExplanation(String commentExplanation) {
        this.commentExplanation = commentExplanation;
    }

    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    public Date getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(Date submitTime) {
        this.submitTime = submitTime;
    }

    public Date getApproveTime() {
        return approveTime;
    }

    public void setApproveTime(Date approveTime) {
        this.approveTime = approveTime;
    }

    public String getSubmitUserId() {
        return submitUserId;
    }

    public void setSubmitUserId(String submitUserId) {
        this.submitUserId = submitUserId;
    }

    public String getApproverUserId() {
        return approverUserId;
    }

    public void setApproverUserId(String approverUserId) {
        this.approverUserId = approverUserId;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public String getSubmitUserName() {
        return submitUserName;
    }

    public void setSubmitUserName(String submitUserName) {
        this.submitUserName = submitUserName;
    }

    public String getApproverUserName() {
        return approverUserName;
    }

    public void setApproverUserName(String approverUserName) {
        this.approverUserName = approverUserName;
    }

    @Override
    public String toString() {
        return "RenewalApproveInfoEntity{" +
                "approveId='" + approveId + '\'' +
                ", parentId='" + parentId + '\'' +
                ", commentExplanation='" + commentExplanation + '\'' +
                ", approveStatus='" + approveStatus + '\'' +
                ", submitTime=" + submitTime +
                ", approveTime=" + approveTime +
                ", submitUserId='" + submitUserId + '\'' +
                ", approverUserId='" + approverUserId + '\'' +
                ", delFlag=" + delFlag +
                ", submitUserName='" + submitUserName + '\'' +
                ", approverUserName='" + approverUserName + '\'' +
                '}';
    }
}
