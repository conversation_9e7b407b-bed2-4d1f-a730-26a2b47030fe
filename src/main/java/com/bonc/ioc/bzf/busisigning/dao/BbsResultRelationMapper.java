package com.bonc.ioc.bzf.busisigning.dao;

import com.bonc.ioc.bzf.busisigning.entity.BbsResultRelationEntity;
import com.bonc.ioc.common.base.mapper.McpBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import com.bonc.ioc.bzf.busisigning.vo.*;
import java.util.List;

/**
 * 人-产品：关系表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2023-08-29
 * @change 2023-08-29 by liwen<PERSON><PERSON> for init
 */
@Mapper
public interface BbsResultRelationMapper extends McpBaseMapper<BbsResultRelationEntity> {
    /**
     * selectByPageCustom 分页查询
     *
     * @param vo 查询条件
     * @return 查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-29
     * @change 2023-08-29 by li<PERSON><PERSON><PERSON> for init
     */
    List<BbsResultRelationPageResultVo> selectByPageCustom(@Param("vo") BbsResultRelationPageVo vo );
}
