package com.bonc.ioc.bzf.busisigning.enums;

/**
 * 审批状态 枚举类
 *
 * <AUTHOR>
 * @since 2023/5/6
 */
public enum ApproveStatusEnum {

    /**
     * 暂存
     */
    TEMP_SAVE("0", "暂存"),

    /**
     * 通过
     */
    APPROVED("1", "通过"),

    /**
     * 未通过
     */
    UNAPPROVED("2", "未通过"),

    /**
     * 待审核
     */
    WAIT_APPROVE("3", "待审核"),

    /**
     * 撤回
     */
    CANCEL("4", "撤回"),

    /**
     * 未发起
     */
    NOT_BEGIN("5", "未发起");;

    /**
     * 编号
     */
    private String code;

    /**
     * 描述信息
     */
    private String desc;

    /**
     * 获取编号
     *
     * @return 编号
     */
    public String getCode() {
        return this.code;
    }

    /**
     * 获取描述信息
     *
     * @return 描述信息
     */
    public String getDesc() {
        return this.desc;
    }

    /**
     * 构造方法
     *
     * @param code 编号
     * @param desc 描述信息
     */
    ApproveStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 构造方法
     */
    ApproveStatusEnum() {
    }

    /**
     * 根据编号查询描述信息
     *
     * @param code 编号
     * @return 描述信息
     */
    public static String getDescByCode(String code) {
        ApproveStatusEnum[] enums = values();
        for (ApproveStatusEnum en : enums) {
            if (en.getCode().equals(code)) {
                return en.getDesc();
            }
        }
        return null;
    }
}
