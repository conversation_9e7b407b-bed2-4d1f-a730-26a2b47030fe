package com.bonc.ioc.bzf.busisigning.utils;

import com.github.junrar.Archive;
import com.github.junrar.rarfile.FileHeader;

import java.io.*;
import java.util.List;

/**
 * rar操作工具类
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2022/7/26 12:56
 */
public class Rar implements Compress {

    @Override
    public void compress(String rootFolderName, List<CompressObject> compressObjects, OutputStream os) throws Exception {

    }

    @Override
    public void compress(String rootFolderName, List<CompressObject> compressObjects, OutputStream os, boolean KeepDirStructure) throws Exception {

    }

    @Override
    public void decompress(File file, DecompressCallback decompressCallback) throws Exception {
      if(file != null){
          Archive archive = new Archive(new FileInputStream(file));
          if (archive != null) {
              FileHeader fileHeader = archive.nextFileHeader();
              while (fileHeader != null) {
                  // 解决中文乱码问题【压缩文件中文乱码】
                  String fullName = fileHeader.getFileNameW().isEmpty() ? fileHeader.getFileNameString() : fileHeader.getFileNameW();
                  if (!fileHeader.isDirectory()) {
                      InputStream is = archive.getInputStream(fileHeader);
                      byte[] bytes = new byte[is.available()];
                      is.read(bytes);
                      is.close();
                      decompressCallback.callback(fullName,bytes);
                  }
                  fileHeader = archive.nextFileHeader();
              }
          }
      }
    }

    /**
     * 解压文件demo
     *
     * @creator YaoChunyu
     * @date 2022/7/26 11:13
     * @createDescribe
     *
     * @modifiedBy
     * @modifieTime
     * @modifieDescribe
     *
     * @param
     * @return
     * @exception
     */
    public void decompressDemo(){
        try {
            File file = new File("E:\\compress.rar");
            decompress(file,(fullName,bytes) -> {
                try {
                    String fileName = fullName.substring(fullName.lastIndexOf("\\")+1);
                    File file2 = new File("E:\\compress.rar"+fileName);
                    OutputStream os = new FileOutputStream(file2);
                    os.write(bytes);
                    os.flush();
                    os.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            });

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        try {
            Rar compress = new Rar();
            compress.decompressDemo();

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
