package com.bonc.ioc.bzf.busisigning.factory.change;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bonc.ioc.bzf.busisigning.config.BusinessServiceConfiguration;
import com.bonc.ioc.bzf.busisigning.config.FeignServiceConfiguration;
import com.bonc.ioc.bzf.busisigning.consts.LogConst;
import com.bonc.ioc.bzf.busisigning.consts.SymbolConst;
import com.bonc.ioc.bzf.busisigning.entity.BbsResultProductEntity;
import com.bonc.ioc.bzf.busisigning.entity.BbsTemplateSeatEntity;
import com.bonc.ioc.bzf.busisigning.entity.BbsiContractChangeApproveEntity;
import com.bonc.ioc.bzf.busisigning.enums.ApproveStatusEnum;
import com.bonc.ioc.bzf.busisigning.enums.BillTypeEnum;
import com.bonc.ioc.bzf.busisigning.enums.ChangeTypeEnum;
import com.bonc.ioc.bzf.busisigning.enums.ContractChangeTypeEnum;
import com.bonc.ioc.bzf.busisigning.enums.RefundMethodEnum;
import com.bonc.ioc.bzf.busisigning.enums.WhetherEnum;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractManagementVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractSignerVo;
import com.bonc.ioc.bzf.busisigning.feign.vo.BbctContractSubjectMatterVo;
import com.bonc.ioc.bzf.busisigning.file.vo.FIleResultVo;
import com.bonc.ioc.bzf.busisigning.file.vo.NewFIleResultVo;
import com.bonc.ioc.bzf.busisigning.utils.DateUtils;
import com.bonc.ioc.bzf.busisigning.utils.ListUtil;
import com.bonc.ioc.bzf.busisigning.utils.ResultUtils;
import com.bonc.ioc.bzf.busisigning.vo.BbctPreviewInfoParamsVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsChangePreviewBillResultVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsContractChangeTemplateVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsiContractChangeVo;
import com.bonc.ioc.bzf.busisigning.vo.SaveConsignorInfoVo;
import com.bonc.ioc.bzf.busisigning.vo.SigningSaveVo;
import com.bonc.ioc.bzf.busisigning.vo.payment.PreviewBillsParamsVo;
import com.bonc.ioc.bzf.busisigning.vo.payment.PreviewBillsResultDeductedAmount;
import com.bonc.ioc.bzf.busisigning.vo.payment.PreviewBillsResultVo;
import com.bonc.ioc.common.exception.McpException;
import com.bonc.ioc.common.util.AppReply;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 合同变更 抽象工厂类
 *
 * <AUTHOR>
 * @since 2024/10/22
 */
@Slf4j
public abstract class AbstractContractChangeFactory {

    /**
     * feign服务 配置实例
     */
    protected FeignServiceConfiguration feignServiceConfiguration;

    /**
     * 业务服务 配置实例
     */
    protected BusinessServiceConfiguration businessServiceConfiguration;

    /**
     * 合同变更信息 vo实体实例
     */
    protected BbsiContractChangeVo contractChangeVo;

    /**
     * 构造方法
     *
     * @param feignServiceConfiguration    feign服务 配置实例
     * @param businessServiceConfiguration 业务服务 配置实例
     * @param contractChangeVo             合同变更信息 vo实体
     */
    protected AbstractContractChangeFactory(FeignServiceConfiguration feignServiceConfiguration,
                                            BusinessServiceConfiguration businessServiceConfiguration,
                                            BbsiContractChangeVo contractChangeVo) {
        this.feignServiceConfiguration = feignServiceConfiguration;
        this.businessServiceConfiguration = businessServiceConfiguration;
        this.contractChangeVo = contractChangeVo;
    }

    /**
     * 获取实例
     *
     * @param contractChangeVo 合同变更信息 vo实体
     * @return 实例
     */
    public static AbstractContractChangeFactory getInstance(BbsiContractChangeVo contractChangeVo) {
        // 获取bean
        FeignServiceConfiguration feignServiceConfiguration = SpringUtil.getBean(FeignServiceConfiguration.class);
        BusinessServiceConfiguration businessServiceConfiguration = SpringUtil.getBean(BusinessServiceConfiguration.class);
        if (ChangeTypeEnum.LESSEE_CHANGE.getCode().equals(contractChangeVo.getChangeType())) {
            // 主承租人变更
            return new LesseeChangeFactory(feignServiceConfiguration,
                    businessServiceConfiguration,
                    contractChangeVo);
        } else if (ChangeTypeEnum.OTHER_CHANGE.getCode().equals(contractChangeVo.getChangeType())) {
            // 其他信息变更
            return new OtherChangeFactory(feignServiceConfiguration,
                    businessServiceConfiguration,
                    contractChangeVo);
        } else {
            throw new McpException(String.format("合同变更类型异常[ccId=%s, changeType=%s]",
                    contractChangeVo.getCcId(), contractChangeVo.getChangeType()));
        }
    }

    /**
     * 获取预览账单信息
     *
     * @return 预览账单信息
     */
    public BbsChangePreviewBillResultVo getPreviewBills() {
        log.info("getPreviewBills开始获取预览账单信息");
        // BbsiContractChangeApproveEntity contractChangeApproveEntity = businessServiceConfiguration
        //         .getContractChangeApproveInfoService()
        //         .selectByCcId(contractChangeVo.getCcId());
        // if (!Objects.isNull(contractChangeApproveEntity) &&
        //         ApproveStatusEnum.APPROVED.getCode().equals(contractChangeApproveEntity.getApproveStatus())) {
        if(ApproveStatusEnum.APPROVED.getCode().equals(contractChangeVo.getChangeStatus())){
            log.info("从本地数据库获取试算信息");
            BbsChangePreviewBillResultVo changePreviewBillResultVo = new BbsChangePreviewBillResultVo();
            //判断是否是缴费周期变更或免租期变更
            if(ContractChangeTypeEnum.PAYMENT_CYCLE_CHANGE.getCode().equals(contractChangeVo.getChangeType())
                || ContractChangeTypeEnum.FREE_RENT_PERIOD_CHANGE.getCode().equals(contractChangeVo.getChangeType())){
                //TODO 改造2 - 从数据库读取新试算接口的JSON数据并转换为实体
                return getChangeTrialPreviewBillsFromDatabase();
            }else{
                 // 租金
                changePreviewBillResultVo.setRentList(businessServiceConfiguration
                        .getChangePreviewBillService()
                        .selectByCcIdAndBillType(contractChangeVo.getCcId(), BillTypeEnum.RENT.getCode()));
                changePreviewBillResultVo.setCashPledgeList(businessServiceConfiguration
                        .getChangePreviewBillService()
                        .selectByCcIdAndBillType(contractChangeVo.getCcId(), BillTypeEnum.CASH_PLEDGE.getCode()));
                changePreviewBillResultVo.setMonthRentList(businessServiceConfiguration
                        .getChangePreviewBillService()
                        .selectByCcIdAndBillType(contractChangeVo.getCcId(), BillTypeEnum.MONTH_RENT.getCode()));
                changePreviewBillResultVo.setDeductibleBillList(businessServiceConfiguration
                        .getChangePreviewBillService()
                        .selectByCcIdAndBillType(contractChangeVo.getCcId(), BillTypeEnum.DEDUCTION.getCode()));
                //物业费
                changePreviewBillResultVo.setPropertyList(businessServiceConfiguration
                        .getChangePreviewBillService()
                        .selectByCcIdAndBillType(contractChangeVo.getCcId(), BillTypeEnum.PROP.getCode()));
                //物业费抵扣
                changePreviewBillResultVo.setDeductiblePropertyBillList(businessServiceConfiguration
                        .getChangePreviewBillService()
                        .selectByCcIdAndBillType(contractChangeVo.getCcId(), BillTypeEnum.PROP_DEDUCTION.getCode()));
                changePreviewBillResultVo.setDeductedAmountList(setDeductibleBillList());
            }
            return changePreviewBillResultVo;
        } else {
            log.info("从工银接口获取试算信息");
             //判断是否是缴费周期变更或免租期变更
            if(ContractChangeTypeEnum.PAYMENT_CYCLE_CHANGE.getCode().equals(contractChangeVo.getChangeType()) 
                || ContractChangeTypeEnum.FREE_RENT_PERIOD_CHANGE.getCode().equals(contractChangeVo.getChangeType())){
                    //TODO 改造3
                return null;
            }else{
                return getChargePreviewBills();
            }
        }
    }

    /**
     * 赋值抵扣当期后盈余金额
     *
     * @return 抵扣当期后盈余金额列表
     */
    private List<PreviewBillsResultDeductedAmount> setDeductibleBillList() {
        List<PreviewBillsResultDeductedAmount> deductibleBillList = new ArrayList<>();
        if (RefundMethodEnum.SUPPLEMENTARY_PAYMENT.getCode().equals(contractChangeVo.getRentRefundPaymentMethod())) {
            // 补缴
            PreviewBillsResultDeductedAmount rentBillAmount = new PreviewBillsResultDeductedAmount();
            rentBillAmount.setChargeSubjectNo("01");
            rentBillAmount.setRentRefundPaymentMethod("1");
            rentBillAmount.setDeductedAmount(StringUtils.isBlank(contractChangeVo.getRentAmountReceivableLease()) ?
                    null : new BigDecimal(contractChangeVo.getRentAmountReceivableLease()));
            deductibleBillList.add(rentBillAmount);
            PreviewBillsResultDeductedAmount cashPledgeBillAmount = new PreviewBillsResultDeductedAmount();
            cashPledgeBillAmount.setChargeSubjectNo("02");
            cashPledgeBillAmount.setRentRefundPaymentMethod("1");
            cashPledgeBillAmount.setDeductedAmount(StringUtils.isBlank(contractChangeVo.getRentAmountReceivable()) ?
                    null : new BigDecimal(contractChangeVo.getRentAmountReceivable()));
            deductibleBillList.add(cashPledgeBillAmount);
        } else if (RefundMethodEnum.RETURN_BANK_CARD.getCode().equals(contractChangeVo.getRentRefundPaymentMethod())) {
            // 退回银行卡
            PreviewBillsResultDeductedAmount rentBillAmount = new PreviewBillsResultDeductedAmount();
            rentBillAmount.setChargeSubjectNo("01");
            rentBillAmount.setRentRefundPaymentMethod("2");
            rentBillAmount.setDeductedAmount(StringUtils.isBlank(contractChangeVo.getRentAmountReceivableLease()) ?
                    null : new BigDecimal(contractChangeVo.getRentAmountReceivableLease()));
            deductibleBillList.add(rentBillAmount);
            PreviewBillsResultDeductedAmount cashPledgeBillAmount = new PreviewBillsResultDeductedAmount();
            cashPledgeBillAmount.setChargeSubjectNo("02");
            cashPledgeBillAmount.setRentRefundPaymentMethod("2");
            cashPledgeBillAmount.setDeductedAmount(StringUtils.isBlank(contractChangeVo.getRentAmountReceivable()) ?
                    null : new BigDecimal(contractChangeVo.getRentAmountReceivable()));
            deductibleBillList.add(cashPledgeBillAmount);
        } else if (RefundMethodEnum.DEDUCTION_RENT.getCode().equals(contractChangeVo.getRentRefundPaymentMethod())) {
            // 抵扣租金
            PreviewBillsResultDeductedAmount rentBillAmount = new PreviewBillsResultDeductedAmount();
            rentBillAmount.setChargeSubjectNo("01");
            rentBillAmount.setRentRefundPaymentMethod("3");
            rentBillAmount.setDeductedAmount(StringUtils.isBlank(contractChangeVo.getRentAmountReceivableLease()) ?
                    null : new BigDecimal(contractChangeVo.getRentAmountReceivableLease()));
            deductibleBillList.add(rentBillAmount);
            PreviewBillsResultDeductedAmount cashPledgeBillAmount = new PreviewBillsResultDeductedAmount();
            cashPledgeBillAmount.setChargeSubjectNo("02");
            cashPledgeBillAmount.setRentRefundPaymentMethod("3");
            cashPledgeBillAmount.setDeductedAmount(StringUtils.isBlank(contractChangeVo.getRentAmountReceivable()) ?
                    null : new BigDecimal(contractChangeVo.getRentAmountReceivable()));
            deductibleBillList.add(cashPledgeBillAmount);
            //物业费抵扣
            PreviewBillsResultDeductedAmount propPledgeBillAmount = new PreviewBillsResultDeductedAmount();
            propPledgeBillAmount.setChargeSubjectNo("07");
            propPledgeBillAmount.setRentRefundPaymentMethod("3");
            propPledgeBillAmount.setDeductedAmount(StringUtils.isBlank(contractChangeVo.getPropertyAmountReceivable()) ?
                    null : new BigDecimal(contractChangeVo.getPropertyAmountReceivable()));
            deductibleBillList.add(propPledgeBillAmount);
        }
        return deductibleBillList;
    }

    /**
     * 赋值签约信息(默认参数)
     *
     * @param parentContractInfoVo 上级合同信息 vo实体
     * @param parentSignInfoVo     上级合同签约信息 vo实体
     * @return 签约信息
     */
    protected SigningSaveVo setDefaultSignInfo(BbctContractManagementVo parentContractInfoVo,
                                               SigningSaveVo parentSignInfoVo) {
        SigningSaveVo signingSaveVo = new SigningSaveVo();
        // 根据上级合同信息赋值签约信息
        setSignInfoByParentContractInfo(signingSaveVo, parentContractInfoVo);
        // 根据上级合同签约信息赋值签约信息
        setSignInfoByParentSignInfo(signingSaveVo, parentSignInfoVo);
        // 根据合同变更信息赋值签约信息
        setSignInfoByChangeInfo(signingSaveVo);
        return signingSaveVo;
    }

    /**
     * 获取签约信息
     *
     * @param contractCode 合同编号
     * @return 签约信息
     */
    protected SigningSaveVo getSignInfoByContractCode(String contractCode) {
        return businessServiceConfiguration
                .getSignInfoExtService()
                .signView(null, contractCode, false);
    }

    /**
     * 获取合同模板信息
     *
     * @return 合同模板信息
     */
    protected BbsContractChangeTemplateVo getContractTemplate() {
        BbsContractChangeTemplateVo contractChangeTemplateVo = businessServiceConfiguration
                .getContractChangeTemplateService()
                .selectByChangeType(contractChangeVo.getChangeType());
        if (Objects.isNull(contractChangeTemplateVo)) {
            throw new McpException("合同模板id不能为空");
        }
        return contractChangeTemplateVo;
    }

    /**
     * 创建模板属性map集
     *
     * @param seatInfoList 模板属性列表
     * @return 模板属性map集
     */
    protected Map<String, String> createTemplateSeatMap(List<BbsTemplateSeatEntity> seatInfoList) {
        Map<String, String> templateSeatMap = new HashMap<>();
        for (BbsTemplateSeatEntity templateSeatEntity : seatInfoList) {
            templateSeatMap.put(templateSeatEntity.getSeatKey(),
                    StringUtils.isNotBlank(templateSeatEntity.getValue()) ? templateSeatEntity.getValue() : templateSeatEntity.getDefaultVal());
        }
        return templateSeatMap;
    }

    /**
     * 获取企业信息
     *
     * @param customerNo 客户编号
     * @return 企业信息
     */
    protected JSONObject getCompanyInfo(String customerNo) {
        AppReply<JSONObject> appReply = feignServiceConfiguration.getCustomerFeignClient().getEnterpriseCustomerByIdOrCreditCode(customerNo);
        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply)) || Objects.isNull(appReply.getData())) {
            throw new McpException(ResultUtils.createLog("客户中心获取企业信息失败[详情: %s, 客户编号: %s]",
                    appReply, customerNo));
        }
        return appReply.getData();
    }

    /**
     * 新增签约信息
     *
     * @param signingSaveVo 签约信息 vo实体
     * @return 签约id
     */
    protected String insertSignInfo(SigningSaveVo signingSaveVo) {
        log.info(String.format("合同变更信息推送签约请求报文[报文=%s]", JSON.toJSONString(signingSaveVo)));
        AppReply<String> appReply = businessServiceConfiguration
                .getSignInfoExtService()
                .saveSignDataContractChange(signingSaveVo);
        log.info(String.format("合同变更信息推送签约响应报文[报文=%s]", JSON.toJSONString(appReply)));
        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply))) {
            throw new McpException(ResultUtils.createLog("推送签约失败[详情:%s, 合同编号:%s, 协议编号:%s]", appReply,
                    contractChangeVo.getContractCode(), contractChangeVo.getAgreementCode()));
        }
        return appReply.getData();
    }

    /**
     * 合同中心 查询合同信息
     *
     * @param contractCode 合同编号
     * @return 合同信息
     */
    protected BbctContractManagementVo getContractInfoByContractCode(String contractCode) {
        AppReply<BbctContractManagementVo> appReply = feignServiceConfiguration.getContractFeignClient().selectByIdNo(contractCode, null);
        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply)) || Objects.isNull(appReply.getData())) {
            throw new McpException(ResultUtils.createLog("查询合同信息失败[详情: %s, contractCode: %s]", appReply, contractCode));
        }
        return appReply.getData();
    }

    /**
     * 合同中心 生成合同pdf文件
     *
     * @param contractManagementVo 合同信息 vo实体
     * @return 合同pdf文件信息 vo实体
     */
    protected FIleResultVo createPdf(BbctContractManagementVo contractManagementVo) {
        AppReply<FIleResultVo> appReply = feignServiceConfiguration.getContractFeignClient().createPdf(contractManagementVo);
        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply))) {
            throw new McpException(ResultUtils.createLog("合同中心生成合同pdf失败[详情: %s, ccId: %s]", appReply, contractChangeVo.getCcId()));
        }
        return appReply.getData();
    }

    /**
     * 缴费中心 获取试算结果
     *
     * @param previewBillsParamsVo 试算账单请求参数 vo实体
     * @return 试算结果
     */
    protected PreviewBillsResultVo getPreviewBills(PreviewBillsParamsVo previewBillsParamsVo) {
        log.info(LogConst.REQUEST_PARAMS_PREFIX + SymbolConst.COLON + JSON.toJSONString(previewBillsParamsVo));
        AppReply<PreviewBillsResultVo> appReply = feignServiceConfiguration.getPaymentV2FeignClient().getPreviewBills(previewBillsParamsVo);
        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply))) {
            throw new McpException(ResultUtils.createLog("获取试算结果失败[详情: %s, ccId: %s]", appReply, contractChangeVo.getCcId()));
        }
        return appReply.getData();
    }

    /**
     * 文件中心 根据文件id获取文件信息
     *
     * @param fileId 试算账单请求参数 vo实体
     * @return 文件信息
     */
    protected List<NewFIleResultVo> getFileInfoById(String fileId) {
        log.info(LogConst.REQUEST_PARAMS_PREFIX + SymbolConst.COLON + JSON.toJSONString(fileId));
        AppReply<List<NewFIleResultVo>> appReply = feignServiceConfiguration.getFileFeignClient().getFileInfoById(fileId);
        if (!Boolean.TRUE.equals(ResultUtils.checkAppReply(appReply)) || Objects.isNull(appReply.getData())) {
            throw new McpException(ResultUtils.createLog("获取文件信息失败[详情: %s, fileId: %s]", appReply, fileId));
        }
        return appReply.getData();
    }

    /**
     * 根据上级合同信息赋值签约信息
     *
     * @param signingSaveVo        签约信息 vo实体
     * @param parentContractInfoVo 上级合同信息 vo实体
     */
    private void setSignInfoByParentContractInfo(SigningSaveVo signingSaveVo,
                                                 BbctContractManagementVo parentContractInfoVo) {
        // 基础信息
        if ("9".equals(parentContractInfoVo.getCashPledgeCode())) {
            signingSaveVo.setCashPledgeValue(String.valueOf(parentContractInfoVo.getDeposit()));
        }
        signingSaveVo.setContractBeginTime(DateUtils.formatDateTime(parentContractInfoVo.getContractBeginTime()));
        signingSaveVo.setContractEndTime(DateUtils.formatDateTime(parentContractInfoVo.getContractEndTime()));
        signingSaveVo.setBusinessFormatStr(parentContractInfoVo.getBusinessFormat());
        signingSaveVo.setBusinessFormatName(parentContractInfoVo.getBusinessFormatName());
        signingSaveVo.setPaymentCycleCode(parentContractInfoVo.getPaymentCycleCode());
        signingSaveVo.setCashPledgeCode(parentContractInfoVo.getCashPledgeCode());
        signingSaveVo.setFirstBankNameCode(parentContractInfoVo.getFirstBankNameCode());
        signingSaveVo.setFirstBankName(parentContractInfoVo.getFirstBankName());
        signingSaveVo.setFirstAccountName(parentContractInfoVo.getFirstAccountName());
        signingSaveVo.setFirstAccountId(parentContractInfoVo.getFirstAccountId());
        // 客户信息
        BbctContractSignerVo userVo = parentContractInfoVo.getUserList().get(0);
        signingSaveVo.setSecondBankTypeCode(userVo.getBankNameCode());
        signingSaveVo.setSecondBankTypeName(userVo.getBankName());
        signingSaveVo.setSecondBankNameCode(userVo.getBankSubbranchCode());
        signingSaveVo.setSecondBankName(userVo.getBankSubbranchName());
        signingSaveVo.setSecondAccountId(userVo.getBankCard());
        signingSaveVo.setSecondAccountName(userVo.getBankUserName());
        // 产品信息
        List<BbctContractSubjectMatterVo> parentProductList = parentContractInfoVo.getSubjectMatterList();
        List<BbsResultProductEntity> productList = new ArrayList<>();
        for (BbctContractSubjectMatterVo parentProductInfo : parentProductList) {
            BbsResultProductEntity resultProductEntity = new BbsResultProductEntity();
            resultProductEntity.setProductNo(parentProductInfo.getProductNo());
            productList.add(resultProductEntity);
        }
        signingSaveVo.setPropertyAddress(productList);
    }

    /**
     * 根据上级合同签约信息赋值签约信息
     *
     * @param signingSaveVo    签约信息 vo实体
     * @param parentSignInfoVo 上级合同签约信息 vo实体
     */
    private void setSignInfoByParentSignInfo(SigningSaveVo signingSaveVo,
                                             SigningSaveVo parentSignInfoVo) {
        signingSaveVo.setContractFees(parentSignInfoVo.getContractFees());
        signingSaveVo.setLeaseCycle(parentSignInfoVo.getLeaseCycle());
        signingSaveVo.setLeaseCycleValue(parentSignInfoVo.getLeaseCycleValue());
        signingSaveVo.setSeatInfoVoList(parentSignInfoVo.getSeatInfoVoList());
        signingSaveVo.setPropertyAddress(parentSignInfoVo.getPropertyAddress());
    }

    /**
     * 根据合同变更信息赋值签约信息
     *
     * @param signingSaveVo 签约信息 vo实体
     */
    private void setSignInfoByChangeInfo(SigningSaveVo signingSaveVo) {
        signingSaveVo.setCcId(contractChangeVo.getCcId());
        signingSaveVo.setContractCode(contractChangeVo.getAgreementCode());
        signingSaveVo.setParentContractCode(contractChangeVo.getContractCode());
        String currentTransactors = String.valueOf(Integer.parseInt(contractChangeVo.getCurrentTransactors()) + 1);
        signingSaveVo.setTransactorType(currentTransactors);
        signingSaveVo.setContractWatermark(WhetherEnum.NO.getCode());
        // 赋值委托代理人信息
        setConsignorInfo(signingSaveVo, currentTransactors);
    }

    /**
     * 赋值委托代理人信息
     *
     * @param signingSaveVo      签约信息 vo实体
     * @param currentTransactors 签约办理人类型
     */
    private void setConsignorInfo(SigningSaveVo signingSaveVo,
                                  String currentTransactors) {
        SaveConsignorInfoVo consignorInfoVo = new SaveConsignorInfoVo();
        consignorInfoVo.setType(currentTransactors);
        consignorInfoVo.setTransactorIdentityCardPhotoUrl(ListUtil.splitBySymbol(contractChangeVo.getTransactorIdentityCardPhotoUrl(), SymbolConst.COMMA));
        consignorInfoVo.setConsignorName(contractChangeVo.getConsignorName());
        consignorInfoVo.setConsignorSignatoryRelationType(contractChangeVo.getConsignorSignatoryRelationType());
        consignorInfoVo.setConsignorCertificationType(contractChangeVo.getConsignorCertificationType());
        consignorInfoVo.setConsignorIdentityCard(contractChangeVo.getConsignorIdentityCard());
        consignorInfoVo.setConsignorMobile(contractChangeVo.getConsignorMobile());
        consignorInfoVo.setConsignorDataUrls(ListUtil.splitBySymbol(contractChangeVo.getConsignorDataUrls(), SymbolConst.COMMA));
        consignorInfoVo.setConsignorIdentityCardPhotoUrl(ListUtil.splitBySymbol(contractChangeVo.getConsignorIdentityCardPhotoUrl(), SymbolConst.COMMA));
        signingSaveVo.setConsignorInfoVo(consignorInfoVo);
    }

    /**
     * 新增预览账单信息
     */
    public abstract void insertPreviewBills();

    /**
     * 试算账单(实时)
     *
     * @return 试算结果 vo实体
     */
    public abstract BbsChangePreviewBillResultVo getChargePreviewBills();

    /**
     * 预览或下载
     *
     * @return 合同文件信息
     */
    public abstract FIleResultVo previewAndDownload();

    /**
     * 赋值子变更预览信息
     *
     * @param previewInfoParamsVo 预览信息参数 vo实体
     */
    public abstract void setSubChangePreviewInfo(BbctPreviewInfoParamsVo previewInfoParamsVo);

    /**
     * 合同变更信息推送给签约
     *
     * @return 签约id
     */
    public abstract String pushSignInfo();
}
