package com.bonc.ioc.bzf.busisigning.feign.vo;

import com.bonc.ioc.common.base.vo.McpBasePageVo;
import com.bonc.ioc.common.validator.inf.InsertValidatorGroup;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 告知书实体
 *
 * <AUTHOR>
 * @date 2022-12-12
 * @change 2022-12-12 by wtl for init
 */
@ApiModel(value = "BbciTempleteVo对象", description = "入住告知书模板表")
public class BbciTempleteVo extends McpBasePageVo implements Serializable {


    /**
     * 告知书id
     */
    @ApiModelProperty(value = "告知书id")
    @NotBlank(message = "告知书id不能为空", groups = {UpdateValidatorGroup.class})
    private String knowId;

    /**
     * 告知书名称
     */
    @ApiModelProperty(value = "告知书名称")
    @NotBlank(message = "告知书名称不能为空", groups = {UpdateValidatorGroup.class, InsertValidatorGroup.class})
    private String knowName;

    /**
     * 告知书内容
     */
    @ApiModelProperty(value = "告知书内容")
    @NotBlank(message = "告知书内容不能为空", groups = {UpdateValidatorGroup.class, InsertValidatorGroup.class})
    private String knowContent;

    /**
     * 告知书模板类型
     */
    @ApiModelProperty(value = "告知书模板类型")
    private String knowType;


    /**
     * isDel
     */
    @ApiModelProperty(value = "删除标识 0-未删除 1-删除")
    private String isDel;

    public String getKnowId() {
        return knowId;
    }

    public void setKnowId(String knowId) {
        this.knowId = knowId;
    }

    public String getKnowName() {
        return knowName;
    }

    public void setKnowName(String knowName) {
        this.knowName = knowName;
    }

    public String getKnowContent() {
        return knowContent;
    }

    public void setKnowContent(String knowContent) {
        this.knowContent = knowContent;
    }

    public String getKnowType() {
        return knowType;
    }

    public void setKnowType(String knowType) {
        this.knowType = knowType;
    }

    public String getIsDel() {
        return isDel;
    }

    public void setIsDel(String isDel) {
        this.isDel = isDel;
    }
}
