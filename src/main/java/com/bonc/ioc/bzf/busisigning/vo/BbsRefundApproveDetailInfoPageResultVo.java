package com.bonc.ioc.bzf.busisigning.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.vo.McpBasePageVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;

import com.bonc.ioc.common.validator.inf.*;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * 退款审批明细表 实体类
 *
 * <AUTHOR>
 * @date 2024-08-28
 * @change 2024-08-28 by pyj for init
 */
@Data
@ApiModel(value = "BbsRefundApproveDetailInfoPageResultVo对象", description = "退款审批明细表")
public class BbsRefundApproveDetailInfoPageResultVo extends McpBasePageVo implements Serializable {

    /**
     * 审批明细id
     */
    @ApiModelProperty(value = "审批明细id")
    @NotBlank(message = "审批明细id不能为空", groups = {UpdateValidatorGroup.class, InsertValidatorGroup.class})
    private String approveDetailId;

    /**
     * 审批id
     */
    @ApiModelProperty(value = "审批id")
    @NotBlank(message = "审批id不能为空", groups = {UpdateValidatorGroup.class})
    private String approveId;

    /**
     * 上级id（退租id）
     */
    @ApiModelProperty(value = "上级id（退租id）")
    private String parentId;

    /**
     * 意见说明
     */
    @ApiModelProperty(value = "意见说明")
    private String commentExplanation;

    /**
     * 审批类型(1.退款审核)
     */
    @ApiModelProperty(value = "审批类型(1.退款审核)")
    private String approveType;

    /**
     * 审批状态(1.通过 2.未通过 3.待审核 4.撤回)
     */
    @ApiModelProperty(value = "审批状态(1.通过 2.未通过 3.待审核 4.撤回)")
    private String approveStatus;

    /**
     * 提交时间
     */
    @ApiModelProperty(value = "提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date submitTime;

    /**
     * 审批时间
     */
    @ApiModelProperty(value = "审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approveTime;

    /**
     * 提交人id
     */
    @ApiModelProperty(value = "提交人id")
    private String submitUserId;

    /**
     * 审批人id
     */
    @ApiModelProperty(value = "审批人id")
    private String approverUserId;

    /**
     * 提交人姓名
     */
    @ApiModelProperty(value = "提交人姓名")
    private String submitUserName;

    /**
     * 审批人姓名
     */
    @ApiModelProperty(value = "审批人姓名")
    private String approverUserName;

    /**
     * 删除标识(1.未删除 0.已删除)
     */
    @ApiModelProperty(value = "删除标识(1.未删除 0.已删除)")
    private Integer delFlag;
}
