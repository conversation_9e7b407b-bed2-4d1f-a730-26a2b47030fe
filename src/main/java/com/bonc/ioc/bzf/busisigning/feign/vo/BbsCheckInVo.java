package com.bonc.ioc.bzf.busisigning.feign.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 签约入住规则表v3.0 推送签约入住实体
 * <AUTHOR>
 * @date 2022-12-03
 * @change 2022-12-03 by ly for init
 */
@ApiModel(value="BbsCheckInVo", description="推送签约入住实体")
@Data
public class BbsCheckInVo extends McpBaseVo implements Serializable{

    /**
     * bbscRuleResultEntity
     */
    @ApiModelProperty(value = "bbscRuleResultEntity")
    private BbsiRuleResultInfoEntity bbscRuleResultEntity;

    /**
     * BbsiRuleInfoEntity
     */
    @ApiModelProperty(value = "BbsiRuleInfoEntity")
    private  BbsiRuleInfoEntity bbsiRuleInfoEntity;

}
