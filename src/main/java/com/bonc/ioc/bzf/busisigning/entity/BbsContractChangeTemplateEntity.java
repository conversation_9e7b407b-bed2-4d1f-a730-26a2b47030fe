package com.bonc.ioc.bzf.busisigning.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.entity.McpBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 合同变更模板表 实体类
 *
 * <AUTHOR>
 * @date 2024-10-25
 * @change 2024-10-25 by pyj for init
 */
@TableName("bbs_contract_change_template")
@ApiModel(value="BbsContractChangeTemplateEntity对象", description="合同变更模板表")
public class BbsContractChangeTemplateEntity extends McpBaseEntity implements Serializable{

    public static final String FIELD_RELATION_ID = "relation_id";
    public static final String FIELD_CHANGE_TYPE = "change_type";
    public static final String FIELD_TEMPLATE_ID = "template_id";
    public static final String FIELD_TEMPLATE_NAME = "template_name";
    public static final String FIELD_DEL_FLAG = "del_flag";
    public static final String FIELD_CREATE_USER_NAME = "create_user_name";

    /**
     * 关联id
     */
    @ApiModelProperty(value = "关联id")
                                @TableId(value = "relation_id", type = IdType.ASSIGN_UUID)
                                  private String relationId;

    /**
     * 变更类型(1合同信息 2.主承租人变更(乙方变更) 3.应缴日期变更 4.其他变更)
     */
    @ApiModelProperty(value = "变更类型(1合同信息 2.主承租人变更(乙方变更) 3.应缴日期变更 4.其他变更)")
                            private String changeType;

    /**
     * 模板id
     */
    @ApiModelProperty(value = "模板id")
                            private String templateId;

    /**
     * 模板名称
     */
    @ApiModelProperty(value = "模板名称")
                            private String templateName;

    /**
     * 删除标识(1.未删除 0.已删除)
     */
    @ApiModelProperty(value = "删除标识(1.未删除 0.已删除)")
                            private Integer delFlag;

    /**
     * 创建人名称
     */
    @ApiModelProperty(value = "创建人名称")
                            private String createUserName;

    /**
     * @return 关联id
     */
    public String getRelationId() {
        return relationId;
    }

    public void setRelationId(String relationId) {
        this.relationId = relationId;
    }

    /**
     * @return 变更类型(1合同信息 2.主承租人变更(乙方变更) 3.应缴日期变更 4.其他变更)
     */
    public String getChangeType() {
        return changeType;
    }

    public void setChangeType(String changeType) {
        this.changeType = changeType;
    }

    /**
     * @return 模板id
     */
    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    /**
     * @return 模板名称
     */
    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    /**
     * @return 删除标识(1.未删除 0.已删除)
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * @return 创建人名称
     */
    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

      @Override
    public String toString() {
        return "BbsContractChangeTemplateEntity{" +
            "relationId=" + relationId +
            ", changeType=" + changeType +
            ", templateId=" + templateId +
            ", templateName=" + templateName +
            ", delFlag=" + delFlag +
            ", createUserName=" + createUserName +
        "}";
    }
}