package com.bonc.ioc.bzf.busisigning.vo;

import com.bonc.ioc.bzf.busisigning.vo.adjust.RentingOutDetailVO;
import com.bonc.ioc.bzf.busisigning.vo.payment.BillInfoVo;
import com.bonc.ioc.bzf.busisigning.vo.payment.ChargeSubjectBillDTO;
import com.bonc.ioc.bzf.busisigning.vo.payment.PreviewBillsResultDeductedAmount;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 预览账单结果 vo实体类
 *
 * <AUTHOR>
 * @since 2024/10/30
 */
@Data
@ApiModel(value = "预览账单结果 vo实体", description = "预览账单结果 vo实体")
public class BbsChangePreviewBillResultVo implements java.io.Serializable {

    /**
     * 租金账单信息列表
     */
    @ApiModelProperty(value = "租金账单信息列表")
    private List<BbsChangePreviewBillVo> rentList;

    /**
     * 保证金账单信息列表
     */
    @ApiModelProperty(value = "保证金账单信息列表")
    private List<BbsChangePreviewBillVo> cashPledgeList;

    /**
     * 月租金
     */
    @ApiModelProperty(value = "月租金账单信息列表")
    private List<BbsChangePreviewBillVo> monthRentList;


    @ApiModelProperty(value = "物业费账单信息列表")
    private List<BbsChangePreviewBillVo> propertyList;

    /**
     * 可抵扣账单列表
     */
    @ApiModelProperty(value = "可抵扣账单列表(押金、租金)")
    private List<BbsChangePreviewBillVo>  deductibleBillList;

    @ApiModelProperty(value = "可抵扣物业费列表")
    private List<BbsChangePreviewBillVo>  deductiblePropertyBillList;

    /**
     * 抵扣当期后盈余金额列表
     */
    @ApiModelProperty(value = "抵扣当期后盈余金额列表")
    private List<PreviewBillsResultDeductedAmount> deductedAmountList;

    @ApiModelProperty(value = "所有可抵扣账单列表(押金、租金)")
    private List<BbsChangePreviewBillVo>  allDeductibleBillList;

    @ApiModelProperty(value = "所有可抵扣物业费列表")
    private List<BbsChangePreviewBillVo>  allDeductiblePropertyBillList;



    /**
     * 缴费周期和免租期 试算后的账单列表 后端把多个houseId的账单合并
     */
    @ApiModelProperty(value = "缴费周期和免租期试算后的账单列表")
    private List<BillInfoVo> billInfoVoList;

    /**
     * 缴费周期和免租期 应退/抵扣金额 后端把多个houseId的数据合并
     */
    @ApiModelProperty(value = "缴费周期和免租期应退/抵扣金额")
    private List<RentingOutDetailVO> rentingOutDetailVOList;
 
}
