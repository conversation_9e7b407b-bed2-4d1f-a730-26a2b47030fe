package com.bonc.ioc.bzf.busisigning.feign.vo;

import com.bonc.ioc.common.dict.util.McpDictPoint;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 *  实体类
 *
 * <AUTHOR>
 * @date 2023-05-17
 * @change 2023-05-17 by gxp for init
 */
@ApiModel(value="BbsChangeRecordPageResultVo对象", description="")
public class BbsChangeRecordPageResultVo extends BasePageResultVo implements Serializable{


    /**
     * 合同变更id
     */
    @ApiModelProperty(value = "合同变更id")
    @NotBlank(message = "合同变更id不能为空",groups = {UpdateValidatorGroup.class})
                                  private String changeId;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
                            private String contractCode;

    /**
     * 签约类型(1散租合同 2趸租合同 3趸租管理协议)
     */
    @ApiModelProperty(value = "签约类型(1散租合同 2趸租合同 3趸租管理协议)")
                            private String signType;

    /**
     * 模板id
     */
    @ApiModelProperty(value = "模板id")
                            private String templentId;

    /**
     * 变更文件id
     */
    @ApiModelProperty(value = "变更文件id")
                            private String changeFileId;

    /**
     * 变更备注
     */
    @ApiModelProperty(value = "变更备注")
                            private String changeRemark;

    /**
     * 变更类型(1主承租人变更 2合同信息变更 3承租家庭购房)
     */
    @ApiModelProperty(value = "变更类型(1主承租人变更 2合同信息变更 3承租家庭购房)")
    @McpDictPoint(dictCode = "CHANGE_TYPE",overTransCopyTo = "changeTypeValue")
    private Integer changeType;

    /**
     * 变更类型中文(1主承租人变更 2合同信息变更 3承租家庭购房)
     */
    @ApiModelProperty(value = "变更类型中文(1主承租人变更 2合同信息变更 3承租家庭购房)")
    private String changeTypeValue;

    /**
     * 变更状态(1待审核 2审核通过 3审核未通过)
     */
    @ApiModelProperty(value = "变更状态(1待审核 2审核通过 3审核未通过)")
    @McpDictPoint(dictCode = "CHANGE_STATUS",overTransCopyTo = "changeStatusValue")
    private Integer changeStatus;

    /**
     * 变更状态中文(1待审核 2审核通过 3审核未通过)
     */
    @ApiModelProperty(value = "变更状态中文(1待审核 2审核通过 3审核未通过)")
    private String changeStatusValue;

    /**
     * 是否附属管理协议（0否 1是）
     */
    @ApiModelProperty(value = "是否附属管理协议（0否 1是）")
    private String affiliatedAgreement;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;

    /**
     * 房源地址
     */
    @ApiModelProperty(value = "房源地址")
                            private String houseAddress;

    /**
     * 租金(元/月)
     */
    @ApiModelProperty(value = "租金(元/月)")
                            private String rent;

    /**
     * 总租金(元/月)
     */
    @ApiModelProperty(value = "总租金(元/月)")
                            private String totalRent;

    /**
     * 面积
     */
    @ApiModelProperty(value = "面积")
                            private String leasedArea;

    /**
     * 总面积
     */
    @ApiModelProperty(value = "总面积")
                            private String totalLeasedArea;

    /**
     * 签约房源套数
     */
    @ApiModelProperty(value = "签约房源套数")
    private Integer setsNum;

    /**
     * 房屋实际用途(01.公租房 07.保租房)
     */
    @ApiModelProperty(value = "房屋实际用途(01.公租房 07.保租房)")
    private String productType;

    /**
     * 房屋实际用途中文(01.公租房 07.保租房)
     */
    @ApiModelProperty(value = "房屋实际用途中文(01.公租房 07.保租房)")
    private String productTypeValue;

    /**
     * 合同开始时间
     */
    @ApiModelProperty(value = "合同开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractBeginTime;

    /**
     * 合同结束时间
     */
    @ApiModelProperty(value = "合同结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date contractEndTime;

    /**
     * 租户编号
     */
    @ApiModelProperty(value = "租户编号")
                            private String customerNo;

    /**
     * 租户名称
     */
    @ApiModelProperty(value = "租户名称")
                            private String customerName;

    /**
     * 租户电话
     */
    @ApiModelProperty(value = "租户电话")
                            private String customerTel;

    /**
     * 协议编号
     */
    @ApiModelProperty(value = "协议编号")
                            private String bankAgreementNo;

    /**
     * 源住址
     */
    @ApiModelProperty(value = "源住址")
                            private String customerHouseAddress;

    /**
     * 单位电话
     */
    @ApiModelProperty(value = "单位电话")
                            private String customerWorkTel;

    /**
     * 开户银行
     */
    @ApiModelProperty(value = "开户银行")
                            private String bankName;

    /**
     * 银行卡号
     */
    @ApiModelProperty(value = "银行卡号")
                            private String bankCard;

    /**
     * 开户行手机号
     */
    @ApiModelProperty(value = "开户行手机号")
                            private String bankPhone;

    /**
     * 是否签署代扣代缴协议（1 是0 否）
     */
    @ApiModelProperty(value = "是否签署代扣代缴协议（1 是0 否）")
                            private String bankIsAgreement;

    /**
     * 是否鉴权（1 是0 否）
     */
    @ApiModelProperty(value = "是否鉴权（1 是0 否）")
                            private String bankIsAuthentication;

    /**
     * 社会统一信息用代码(企业使用)
     */
    @ApiModelProperty(value = "社会统一信息用代码(企业使用)")
                            private String customerCreditCode;

    /**
     * 单位编号
     */
    @ApiModelProperty(value = "单位编号")
                            private String unitId;

    /**
     * 单位名称
     */
    @ApiModelProperty(value = "单位名称")
                            private String unitName;

    /**
     * 通讯地址
     */
    @ApiModelProperty(value = "通讯地址")
                            private String mailAddress;

    /**
     * 邮政编码
     */
    @ApiModelProperty(value = "邮政编码")
                            private String postalCode;

    /**
     * 法定代表人
     */
    @ApiModelProperty(value = "法定代表人")
                            private String legalName;

    /**
     * 法定代表人联系电话
     */
    @ApiModelProperty(value = "法定代表人联系电话")
                            private String legalMobile;

    /**
     * 委托代理人姓名
     */
    @ApiModelProperty(value = "委托代理人姓名")
                            private String consignorName;

    /**
     * 委托代理人联系电话
     */
    @ApiModelProperty(value = "委托代理人联系电话")
                            private String consignorMobile;

    /**
     * 删除标识(1未删除 0已删除)
     */
    @ApiModelProperty(value = "删除标识(1未删除 0已删除)")
                            private Integer delFlag;

    /**
     * 创建人名字
     */
    /*@ApiModelProperty(value = "创建人名字")
                            private String createUserName;
*/
    /**
     * 修改人名字
     */
    /*@ApiModelProperty(value = "修改人名字")
                            private String modifyUserName;
*/
    /**
     * 登录人ID
     */
    @ApiModelProperty(value = "登录人ID")
    private String loginUser;

    /**
     * 登录人租户ID
     */
    @ApiModelProperty(value = "登录人租户ID")
    private String loginTenantId;

    /**
     * 创建时间开始
     */
    @ApiModelProperty(value = "创建时间开始")
    private String createTimeStart;

    /**
     * 创建时间结束
     */
    @ApiModelProperty(value = "创建时间结束")
    private String createTimeEnd;

    /**
     * @return 合同变更id
     */
    public String getChangeId() {
        return changeId;
    }

    public void setChangeId(String changeId) {
        this.changeId = changeId;
    }

    /**
     * @return 合同编号
     */
    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    /**
     * @return 签约类型(1散租合同 2趸租合同 3趸租管理协议)
     */
    public String getSignType() {
        return signType;
    }

    public void setSignType(String signType) {
        this.signType = signType;
    }

    /**
     * @return 模板id
     */
    public String getTemplentId() {
        return templentId;
    }

    public void setTemplentId(String templentId) {
        this.templentId = templentId;
    }

    /**
     * @return 变更文件id
     */
    public String getChangeFileId() {
        return changeFileId;
    }

    public void setChangeFileId(String changeFileId) {
        this.changeFileId = changeFileId;
    }

    /**
     * @return 变更备注
     */
    public String getChangeRemark() {
        return changeRemark;
    }

    public void setChangeRemark(String changeRemark) {
        this.changeRemark = changeRemark;
    }

    /**
     * @return 变更类型(1主承租人变更 2合同信息变更 3承租家庭购房)
     */
    public Integer getChangeType() {
        return changeType;
    }

    public void setChangeType(Integer changeType) {
        this.changeType = changeType;
    }

    /**
     * @return 变更类型中文(1主承租人变更 2合同信息变更 3承租家庭购房)
     */
    public String getChangeTypeValue() {
        return changeTypeValue;
    }

    public void setChangeTypeValue(String changeTypeValue) {
        this.changeTypeValue = changeTypeValue;
    }

    /**
     * @return 变更状态(1待审核 2审核通过 3审核未通过)
     */
    public Integer getChangeStatus() {
        return changeStatus;
    }

    public void setChangeStatus(Integer changeStatus) {
        this.changeStatus = changeStatus;
    }

    /**
     * @return 变更状态中文(1待审核 2审核通过 3审核未通过)
     */
    public String getChangeStatusValue() {
        return changeStatusValue;
    }

    public void setChangeStatusValue(String changeStatusValue) {
        this.changeStatusValue = changeStatusValue;
    }

    /**
     * @return 是否附属管理协议
     */
    public String getAffiliatedAgreement() {
        return affiliatedAgreement;
    }

    public void setAffiliatedAgreement(String affiliatedAgreement) {
        this.affiliatedAgreement = affiliatedAgreement;
    }

    /**
     * @return 项目ID
     */
    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    /**
     * @return 房源地址
     */
    public String getHouseAddress() {
        return houseAddress;
    }

    public void setHouseAddress(String houseAddress) {
        this.houseAddress = houseAddress;
    }

    /**
     * @return 租金(元/月)
     */
    public String getRent() {
        return rent;
    }

    public void setRent(String rent) {
        this.rent = rent;
    }

    /**
     * @return 总租金(元/月)
     */
    public String getTotalRent() {
        return totalRent;
    }

    public void setTotalRent(String totalRent) {
        this.totalRent = totalRent;
    }

    /**
     * @return 面积
     */
    public String getLeasedArea() {
        return leasedArea;
    }

    public void setLeasedArea(String leasedArea) {
        this.leasedArea = leasedArea;
    }

    /**
     * @return 总面积
     */
    public String getTotalLeasedArea() {
        return totalLeasedArea;
    }

    public void setTotalLeasedArea(String totalLeasedArea) {
        this.totalLeasedArea = totalLeasedArea;
    }

    /**
     * @return 签约房源套数
     */
    public Integer getSetsNum() {
        return setsNum;
    }

    public void setSetsNum(Integer setsNum) {
        this.setsNum = setsNum;
    }

    /**
     * @return 房屋实际用途(01.公租房 07.保租房)
     */
    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    /**
     * @return 房屋实际用途中文(01.公租房 07.保租房)
     */
    public String getProductTypeValue() {
        return productTypeValue;
    }

    public void setProductTypeValue(String productTypeValue) {
        this.productTypeValue = productTypeValue;
    }

    /**
     * @return 合同开始时间
     */
    public Date getContractBeginTime() {
        return contractBeginTime;
    }

    public void setContractBeginTime(Date contractBeginTime) {
        this.contractBeginTime = contractBeginTime;
    }

    /**
     * @return 合同结束时间
     */
    public Date getContractEndTime() {
        return contractEndTime;
    }

    public void setContractEndTime(Date contractEndTime) {
        this.contractEndTime = contractEndTime;
    }

    /**
     * @return 租户编号
     */
    public String getCustomerNo() {
        return customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    /**
     * @return 租户名称
     */
    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    /**
     * @return 租户电话
     */
    public String getCustomerTel() {
        return customerTel;
    }

    public void setCustomerTel(String customerTel) {
        this.customerTel = customerTel;
    }

    /**
     * @return 协议编号
     */
    public String getBankAgreementNo() {
        return bankAgreementNo;
    }

    public void setBankAgreementNo(String bankAgreementNo) {
        this.bankAgreementNo = bankAgreementNo;
    }

    /**
     * @return 源住址
     */
    public String getCustomerHouseAddress() {
        return customerHouseAddress;
    }

    public void setCustomerHouseAddress(String customerHouseAddress) {
        this.customerHouseAddress = customerHouseAddress;
    }

    /**
     * @return 单位电话
     */
    public String getCustomerWorkTel() {
        return customerWorkTel;
    }

    public void setCustomerWorkTel(String customerWorkTel) {
        this.customerWorkTel = customerWorkTel;
    }

    /**
     * @return 开户银行
     */
    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    /**
     * @return 银行卡号
     */
    public String getBankCard() {
        return bankCard;
    }

    public void setBankCard(String bankCard) {
        this.bankCard = bankCard;
    }

    /**
     * @return 开户行手机号
     */
    public String getBankPhone() {
        return bankPhone;
    }

    public void setBankPhone(String bankPhone) {
        this.bankPhone = bankPhone;
    }

    /**
     * @return 是否签署代扣代缴协议（1 是0 否）
     */
    public String getBankIsAgreement() {
        return bankIsAgreement;
    }

    public void setBankIsAgreement(String bankIsAgreement) {
        this.bankIsAgreement = bankIsAgreement;
    }

    /**
     * @return 是否鉴权（1 是0 否）
     */
    public String getBankIsAuthentication() {
        return bankIsAuthentication;
    }

    public void setBankIsAuthentication(String bankIsAuthentication) {
        this.bankIsAuthentication = bankIsAuthentication;
    }

    /**
     * @return 社会统一信息用代码(企业使用)
     */
    public String getCustomerCreditCode() {
        return customerCreditCode;
    }

    public void setCustomerCreditCode(String customerCreditCode) {
        this.customerCreditCode = customerCreditCode;
    }

    /**
     * @return 单位编号
     */
    public String getUnitId() {
        return unitId;
    }

    public void setUnitId(String unitId) {
        this.unitId = unitId;
    }

    /**
     * @return 单位名称
     */
    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    /**
     * @return 通讯地址
     */
    public String getMailAddress() {
        return mailAddress;
    }

    public void setMailAddress(String mailAddress) {
        this.mailAddress = mailAddress;
    }

    /**
     * @return 邮政编码
     */
    public String getPostalCode() {
        return postalCode;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }

    /**
     * @return 法定代表人
     */
    public String getLegalName() {
        return legalName;
    }

    public void setLegalName(String legalName) {
        this.legalName = legalName;
    }

    /**
     * @return 法定代表人联系电话
     */
    public String getLegalMobile() {
        return legalMobile;
    }

    public void setLegalMobile(String legalMobile) {
        this.legalMobile = legalMobile;
    }

    /**
     * @return 委托代理人姓名
     */
    public String getConsignorName() {
        return consignorName;
    }

    public void setConsignorName(String consignorName) {
        this.consignorName = consignorName;
    }

    /**
     * @return 委托代理人联系电话
     */
    public String getConsignorMobile() {
        return consignorMobile;
    }

    public void setConsignorMobile(String consignorMobile) {
        this.consignorMobile = consignorMobile;
    }

    /**
     * @return 删除标识(1未删除 0已删除)
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * @return 创建人名字
     */
    /*public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }
*/
    /**
     * @return 修改人名字
     */
    /*public String getModifyUserName() {
        return modifyUserName;
    }

    public void setModifyUserName(String modifyUserName) {
        this.modifyUserName = modifyUserName;
    }
*/
    /**
     * @return 登录人ID
     */
    public String getLoginUser() {
        return loginUser;
    }

    public void setLoginUser(String loginUser) {
        this.loginUser = loginUser;
    }

    /**
     * @return 登录人租户ID
     */
    public String getLoginTenantId() {
        return loginTenantId;
    }

    public void setLoginTenantId(String loginTenantId) {
        this.loginTenantId = loginTenantId;
    }

    /**
     * @return 创建时间开始
     */
    public String getCreateTimeStart(){
        return createTimeStart;
    }

    public void setCreateTimeStart(String createTimeStart) {
        this.createTimeStart = createTimeStart;
    }

    /**
     * @return 创建时间结束
     */
    public String getCreateTimeEnd(){
        return createTimeEnd;
    }

    public void setCreateTimeEnd(String createTimeEnd) {
        this.createTimeEnd = createTimeEnd;
    }

    @Override
    public String toString() {
        return "BbsChangeRecordPageResultVo{" +
            "changeId=" + changeId +
            ", contractCode=" + contractCode +
            ", signType=" + signType +
            ", templentId=" + templentId +
            ", changeFileId=" + changeFileId +
            ", changeRemark=" + changeRemark +
            ", changeType=" + changeType +
            ", changeStatus=" + changeStatus +
            ", projectId=" + projectId +
            ", houseAddress=" + houseAddress +
            ", rent=" + rent +
            ", totalRent=" + totalRent +
            ", leasedArea=" + leasedArea +
            ", totalLeasedArea=" + totalLeasedArea +
            ", setsNum=" + setsNum +
            ", productType=" + productType +
            ", contractBeginTime=" + contractBeginTime +
            ", contractEndTime=" + contractEndTime +
            ", customerNo=" + customerNo +
            ", customerName=" + customerName +
            ", customerTel=" + customerTel +
            ", bankAgreementNo=" + bankAgreementNo +
            ", customerHouseAddress=" + customerHouseAddress +
            ", customerWorkTel=" + customerWorkTel +
            ", bankName=" + bankName +
            ", bankCard=" + bankCard +
            ", bankPhone=" + bankPhone +
            ", bankIsAgreement=" + bankIsAgreement +
            ", bankIsAuthentication=" + bankIsAuthentication +
            ", customerCreditCode=" + customerCreditCode +
            ", unitId=" + unitId +
            ", unitName=" + unitName +
            ", mailAddress=" + mailAddress +
            ", postalCode=" + postalCode +
            ", legalName=" + legalName +
            ", legalMobile=" + legalMobile +
            ", consignorName=" + consignorName +
            ", consignorMobile=" + consignorMobile +
            ", delFlag=" + delFlag +
            /*", createUserName=" + createUserName +
            ", modifyUserName=" + modifyUserName +*/
            ", createTimeStart=" + createTimeStart +
            ", createTimeEnd=" + createTimeEnd +
        "}";
    }
}
