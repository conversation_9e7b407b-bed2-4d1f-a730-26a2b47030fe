package com.bonc.ioc.bzf.busisigning.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: l<PERSON><PERSON><PERSON><PERSON>
 * @createDate: 2023-09-12 09:29
 * @Version 1.0
 **/
@Data
public class Depos {
    /**
     * 缴费次数
     * */
    private String chargePeriod;
    /**
     * 缴费开始时间
     * */
    private String chargeStartDate;
    /**
     * 缴费结束时间
     * */
    private String chargeEndDate;
    /**
     * 租金单价
     * */
    private String paramprice;
    /**
     * 面积
     * */
    private String paramarea;
    /**
     * 月数
     * */
    private Integer months;
    /**
     * 天数
     * */
    private Integer days;
    /**
     * 应缴金额
     * */
    private BigDecimal payableMoney;
    /**
     * 增值税率
     * */
    private BigDecimal taxRate;
    /**
     * 不含税金额
     * */
    private BigDecimal excludingRateMoney;
    /**
     * 增值税额
     * */
    private BigDecimal rateMoney;
    /**
     * 日租金
     * */
    private BigDecimal dayMoney;
    /**
     * 房源名称
     * */
    private String houseName;
    /**
     * 房屋ID
     * */
    private String houseId;
    /**
     * 月租金
     * */
    private BigDecimal monthAmount;
    /**
     * 备注
     * */
    private String notes;


    /**
     * 计费科目编号
     * */
    private String chargeSubjectNo;
}
