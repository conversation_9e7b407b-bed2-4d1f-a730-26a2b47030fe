package com.bonc.ioc.bzf.busisigning.vo;

import lombok.Data;

import java.util.List;

/**
 * @author: li<PERSON><PERSON><PERSON>
 * @createDate: 2023-09-01 16:59
 * @Version 1.0
 **/
@Data
public class SaveConsignorInfoVo {
    /**
     * 签约ID
     * */
    private String signId;
    /**
     * 1.本人办理 2.委托人办理
     * */
    private String type;
    /**
     * 合同编号
     * */
    private String contractCode;
    /**
     * 委托代理人姓名
     * */
    private String consignorName;
    /**
     * 与主承租人关系
     * */
    private String consignorSignatoryRelationType;
    /**
     * 证件类型
     * */
    private String consignorCertificationType;
    /**
     * 证件号码
     * */
    private String consignorIdentityCard;
    /**
     * 手机号码
     * */
    private String consignorMobile;
    /**
     * 上传委托材料 文件id 集合
     * */
    private List<String> consignorDataUrls;
    /**
     * 委托人身份证正反面照片 文件ID
     * */
    private List<String> consignorIdentityCardPhotoUrl;
    /**
     * 本人身份证正反面照片 文件ID
     * */
    private List<String> transactorIdentityCardPhotoUrl;

    /**
     * requestId
     */
    private String requestId;

    /**
     * 合同fileID
     * */
    private String fileId;

    /**
     * 业务分类编号
     */
    private String businessTypeCode;


}
