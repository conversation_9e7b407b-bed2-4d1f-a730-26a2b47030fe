package com.bonc.ioc.bzf.busisigning.feign.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 机构客户-房关系添加操作结果
 *
 * @date 2023-06-09
 * @change 2023-06-09 by wangkz for init
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "OrgCustomerHouseRelInsertResultVo对象", description = "机构客户-房关系添加操作结果")
public class OrgCustomerHouseRelInsertResultVo extends McpBaseVo implements Serializable {

    /**
     * 是否成功,0:失败;1:成功;2:已有对应数据;3:存在没有对应数据的字段;99:发生异常
     */
    @ApiModelProperty(value = "是否成功,0:失败;1:成功;2:已有对应数据;3:存在没有对应数据的字段;99:发生异常")
    private String operationResult;

    /**
     * 机构客户-房关系ID
     */
    @ApiModelProperty(value = "机构客户-房关系ID(成功才有值)")
    private String orgCustomerHouseRelId;

    /**
     * 错误原因
     */
    @ApiModelProperty(value = "错误原因(失败才有值)")
    private String errorCause;

    /**
     * 接收到的数据
     */
    @ApiModelProperty(value = "接收到的数据")
    private OrgCustomerHouseRelInsertVo orgCustomerHouseRelInsertVo;
}
