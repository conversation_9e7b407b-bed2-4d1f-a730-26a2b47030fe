package com.bonc.ioc.bzf.busisigning.feign.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ContractChangeBankInfo {

    @ApiModelProperty("合同编号")
    private String contractNo;

    @ApiModelProperty("开户行")
    private String bankName;

    @ApiModelProperty("银行编号")
    private String bankCode;

    /**
     * 银行卡号
     */
    @ApiModelProperty("银行卡号")
    private String bankCard;

    /**
     * 开户行手机号
     */
    @ApiModelProperty("开户行手机号")
    private String bankPhone;

}
