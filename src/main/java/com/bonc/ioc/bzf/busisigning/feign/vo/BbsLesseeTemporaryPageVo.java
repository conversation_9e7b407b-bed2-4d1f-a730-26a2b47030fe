package com.bonc.ioc.bzf.busisigning.feign.vo;

import com.bonc.ioc.common.base.vo.McpBasePageVo;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 承租人临时表 实体类
 *
 * <AUTHOR>
 * @date 2023-05-09
 * @change 2023-05-09 by lyh for init
 */
@ApiModel(value="BbsLesseeTemporaryPageVo对象", description="承租人临时表")
public class BbsLesseeTemporaryPageVo extends McpBasePageVo implements Serializable{


    /**
     * 承租人临时表ID
     */
    @ApiModelProperty(value = "承租人临时表ID")
    @NotBlank(message = "承租人临时表ID不能为空",groups = {UpdateValidatorGroup.class})
                                  private String lesseeId;

    /**
     * 合同变更id（外键）
     */
    @ApiModelProperty(value = "合同变更id（外键）")
                            private String changeId;

    /**
     * 合同编号(与签约表关联外键)
     */
    @ApiModelProperty(value = "合同编号(与签约表关联外键)")
                            private String contractCode;

    /**
     * 签约类型(1散租 2趸租 3管理协议)
     */
    @ApiModelProperty(value = "签约类型(1散租 2趸租 3管理协议)")
                            private String signType;

    /**
     * 承租人姓名
     */
    @ApiModelProperty(value = "承租人姓名")
                            private String tenantName;

    /**
     * 承租人电话
     */
    @ApiModelProperty(value = "承租人电话")
                            private String tenantTel;

    /**
     * 证件号码
     */
    @ApiModelProperty(value = "证件号码")
    private String customerIdNumber;

    /**
     * 家庭编号
     */
    @ApiModelProperty(value = "家庭编号")
    private String publicRentalFilingNum;

    /**
     * 协议编号
     */
    @ApiModelProperty(value = "协议编号")
    private String bankAgreementNo;

    /**
     * 开户银行编号
     */
    @ApiModelProperty(value = "开户银行编号")
    private String bankCode;

    /**
     * 开户银行(1.北京银行)
     */
    @ApiModelProperty(value = "开户银行(1.北京银行)")
                            private String depositBank;

    /**
     * 银行卡号
     */
    @ApiModelProperty(value = "银行卡号")
                            private String bankCard;

    /**
     * 银行预留手机号
     */
    @ApiModelProperty(value = "银行预留手机号")
                            private String bankPhone;

    /**
     * 原住址
     */
    @ApiModelProperty(value = "原住址")
                            private String tenantHouseAddress;

    /**
     * 单位电话
     */
    @ApiModelProperty(value = "单位电话")
                            private String tenantWorkTel;

    /**
     * 紧急情况联系人姓名
     */
    @ApiModelProperty(value = "紧急情况联系人姓名")
                            private String emergentName;

    /**
     * 紧急情况联系人性别
     */
    @ApiModelProperty(value = "紧急情况联系人性别")
                            private String emergentGender;

    /**
     * 紧急情况联系人电话
     */
    @ApiModelProperty(value = "紧急情况联系人电话")
                            private String emergentTel;

    /**
     * 趸租单位名称
     */
    @ApiModelProperty(value = "趸租单位名称")
                            private String unitName;

    /**
     * 通讯地址
     */
    @ApiModelProperty(value = "通讯地址")
                            private String mailAddress;

    /**
     * 邮政编码
     */
    @ApiModelProperty(value = "邮政编码")
                            private String postalCode;

    /**
     * 法定代表人
     */
    @ApiModelProperty(value = "法定代表人")
                            private String legalName;

    /**
     * 法定代表人联系电话
     */
    @ApiModelProperty(value = "法定代表人联系电话")
                            private String legalMobile;

    /**
     * 委托代理人姓名
     */
    @ApiModelProperty(value = "委托代理人姓名")
                            private String consignorName;

    /**
     * 委托代理人联系电话
     */
    @ApiModelProperty(value = "委托代理人联系电话")
                            private String consignorMobile;

    /**
     * 是否已变更（01变更前，02变更后）
     */
    @ApiModelProperty(value = "是否已变更（01变更前，02变更后）")
                            private String isChange;

    /**
     * 趸租管理协议协议编号
     */
    @ApiModelProperty(value = "趸租管理协议协议编号")
    private String agreementNo;

    /**
     * 所属趸租单位
     */
    @ApiModelProperty(value = "所属趸租单位")
    private String belongUnitName;

    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码")
    private String tenantCreditCode;

    /**
     * 删除标识(1.未删除 0.已删除)
     */
    @ApiModelProperty(value = "删除标识(1.未删除 0.已删除)")
                            private Integer delFlag;

    /**
     * 散户或者企业编号
     */
    @ApiModelProperty(value = "散户或者企业编号")
    private String customerNo;


    /**
     * 是否签署代扣代缴协议
     */
    @ApiModelProperty(value = "是否签署代扣代缴协议")
    private String bankIsAgreement;

    /**
     * 是否鉴权（1 是0 否）
     */
    @ApiModelProperty(value = "是否鉴权（1 是0 否）")
    private String bankIsAuthentication;

    /**
     * 签署类型(1.手机号开通代扣代缴服务 2.线下已经开通代扣代缴服务)
     */
    @ApiModelProperty(value = "签署类型(1.手机号开通代扣代缴服务 2.线下已经开通代扣代缴服务)")
    private String bankAgreementType;


    /**
     * 上传文件
     */
    @ApiModelProperty(value = "上传文件")
    private String uploadFiles;


    /**
     * 是否有银行卡信息(1有  2无)
     */
    @ApiModelProperty(value = "是否有银行卡信息(1有  2无)")
    private String bankInfoNo;

    /**
     * @return 是否有银行卡信息(1有  2无)
     */
    public String getBankInfoNo() {
        return bankInfoNo;
    }

    public void setBankInfoNo(String bankInfoNo) {
        this.bankInfoNo = bankInfoNo;
    }

    /**
     * @return 上传文件
     */
    public String getUploadFiles() {
        return uploadFiles;
    }

    public void setUploadFiles(String uploadFiles) {
        this.uploadFiles = uploadFiles;
    }

    /**
     * @return 是否签署代扣代缴协议
     */
    public String getBankIsAgreement() {
        return bankIsAgreement;
    }

    public void setBankIsAgreement(String bankIsAgreement) {
        this.bankIsAgreement = bankIsAgreement;
    }

    /**
     * @return 是否鉴权（1 是0 否）
     */
    public String getBankIsAuthentication() {
        return bankIsAuthentication;
    }

    public void setBankIsAuthentication(String bankIsAuthentication) {
        this.bankIsAuthentication = bankIsAuthentication;
    }

    /**
     * @return 签署类型(1.手机号开通代扣代缴服务 2.线下已经开通代扣代缴服务)
     */
    public String getBankAgreementType() {
        return bankAgreementType;
    }

    public void setBankAgreementType(String bankAgreementType) {
        this.bankAgreementType = bankAgreementType;
    }


    /**
     * @return 散户或者企业编号
     */
    public String getCustomerNo() {
        return customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }


    /**
     * @return 承租人临时表ID
     */
    public String getLesseeId() {
        return lesseeId;
    }

    public void setLesseeId(String lesseeId) {
        this.lesseeId = lesseeId;
    }

    /**
     * @return 合同变更id（外键）
     */
    public String getChangeId() {
        return changeId;
    }

    public void setChangeId(String changeId) {
        this.changeId = changeId;
    }

    /**
     * @return 合同编号(与签约表关联外键)
     */
    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    /**
     * @return 签约类型(1散租 2趸租 3管理协议)
     */
    public String getSignType() {
        return signType;
    }

    public void setSignType(String signType) {
        this.signType = signType;
    }

    /**
     * @return 承租人姓名
     */
    public String getTenantName() {
        return tenantName;
    }

    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }

    /**
     * @return 承租人电话
     */
    public String getTenantTel() {
        return tenantTel;
    }

    public void setTenantTel(String tenantTel) {
        this.tenantTel = tenantTel;
    }

    /**
     * @return 证件号码
     */
    public String getCustomerIdNumber() {
        return customerIdNumber;
    }

    public void setCustomerIdNumber(String customerIdNumber) {
        this.customerIdNumber = customerIdNumber;
    }

    /**
     * @return 家庭编号
     */
    public String getPublicRentalFilingNum() {
        return publicRentalFilingNum;
    }

    public void setPublicRentalFilingNum(String publicRentalFilingNum) {
        this.publicRentalFilingNum = publicRentalFilingNum;
    }

    /**
     * @return 协议编号
     */
    public String getBankAgreementNo() {
        return bankAgreementNo;
    }

    public void setBankAgreementNo(String bankAgreementNo) {
        this.bankAgreementNo = bankAgreementNo;
    }

    /**
     * @return 开户银行编号
     */
    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    /**
     * @return 开户银行(1.北京银行)
     */
    public String getDepositBank() {
        return depositBank;
    }

    public void setDepositBank(String depositBank) {
        this.depositBank = depositBank;
    }

    /**
     * @return 银行卡号
     */
    public String getBankCard() {
        return bankCard;
    }

    public void setBankCard(String bankCard) {
        this.bankCard = bankCard;
    }

    /**
     * @return 银行预留手机号
     */
    public String getBankPhone() {
        return bankPhone;
    }

    public void setBankPhone(String bankPhone) {
        this.bankPhone = bankPhone;
    }

    /**
     * @return 原住址
     */
    public String getTenantHouseAddress() {
        return tenantHouseAddress;
    }

    public void setTenantHouseAddress(String tenantHouseAddress) {
        this.tenantHouseAddress = tenantHouseAddress;
    }

    /**
     * @return 单位电话
     */
    public String getTenantWorkTel() {
        return tenantWorkTel;
    }

    public void setTenantWorkTel(String tenantWorkTel) {
        this.tenantWorkTel = tenantWorkTel;
    }

    /**
     * @return 紧急情况联系人姓名
     */
    public String getEmergentName() {
        return emergentName;
    }

    public void setEmergentName(String emergentName) {
        this.emergentName = emergentName;
    }

    /**
     * @return 紧急情况联系人性别
     */
    public String getEmergentGender() {
        return emergentGender;
    }

    public void setEmergentGender(String emergentGender) {
        this.emergentGender = emergentGender;
    }

    /**
     * @return 紧急情况联系人电话
     */
    public String getEmergentTel() {
        return emergentTel;
    }

    public void setEmergentTel(String emergentTel) {
        this.emergentTel = emergentTel;
    }

    /**
     * @return 趸租单位名称
     */
    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    /**
     * @return 通讯地址
     */
    public String getMailAddress() {
        return mailAddress;
    }

    public void setMailAddress(String mailAddress) {
        this.mailAddress = mailAddress;
    }

    /**
     * @return 邮政编码
     */
    public String getPostalCode() {
        return postalCode;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }

    /**
     * @return 法定代表人
     */
    public String getLegalName() {
        return legalName;
    }

    public void setLegalName(String legalName) {
        this.legalName = legalName;
    }

    /**
     * @return 法定代表人联系电话
     */
    public String getLegalMobile() {
        return legalMobile;
    }

    public void setLegalMobile(String legalMobile) {
        this.legalMobile = legalMobile;
    }

    /**
     * @return 委托代理人姓名
     */
    public String getConsignorName() {
        return consignorName;
    }

    public void setConsignorName(String consignorName) {
        this.consignorName = consignorName;
    }

    /**
     * @return 委托代理人联系电话
     */
    public String getConsignorMobile() {
        return consignorMobile;
    }

    public void setConsignorMobile(String consignorMobile) {
        this.consignorMobile = consignorMobile;
    }

    /**
     * @return 是否已变更（01变更前，02变更后）
     */
    public String getIsChange() {
        return isChange;
    }

    public void setIsChange(String isChange) {
        this.isChange = isChange;
    }

    /**
     * @return 趸租管理协议协议编号
     */
    public String getAgreementNo() {
        return agreementNo;
    }

    public void setAgreementNo(String agreementNo) {
        this.agreementNo = agreementNo;
    }

    /**
     * @return 所属趸租单位
     */
    public String getBelongUnitName() {
        return belongUnitName;
    }

    public void setBelongUnitName(String belongUnitName) {
        this.belongUnitName = belongUnitName;
    }

    /**
     * @return 统一社会信用代码
     */
    public String getTenantCreditCode() {
        return tenantCreditCode;
    }

    public void setTenantCreditCode(String tenantCreditCode) {
        this.tenantCreditCode = tenantCreditCode;
    }

    /**
     * @return 删除标识(1.未删除 0.已删除)
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

      @Override
    public String toString() {
        return "BbsLesseeTemporaryPageVo{" +
            "lesseeId=" + lesseeId +
            ", changeId=" + changeId +
            ", contractCode=" + contractCode +
            ", customerNo=" + customerNo +
            ", signType=" + signType +
            ", tenantName=" + tenantName +
            ", tenantTel=" + tenantTel +
            ", customerIdNumber=" + customerIdNumber +
            ", publicRentalFilingNum=" + publicRentalFilingNum +
            ", bankAgreementNo=" + bankAgreementNo +
            ", bankInfoNo=" + bankInfoNo +
            ", bankCode=" + bankCode +
            ", depositBank=" + depositBank +
            ", bankCard=" + bankCard +
            ", bankPhone=" + bankPhone +
            ", bankIsAgreement=" + bankIsAgreement +
            ", bankIsAuthentication=" + bankIsAuthentication +
            ", bankAgreementType=" + bankAgreementType +
            ", uploadFiles=" + uploadFiles +
            ", tenantHouseAddress=" + tenantHouseAddress +
            ", tenantWorkTel=" + tenantWorkTel +
            ", emergentName=" + emergentName +
            ", emergentGender=" + emergentGender +
            ", emergentTel=" + emergentTel +
            ", unitName=" + unitName +
            ", mailAddress=" + mailAddress +
            ", postalCode=" + postalCode +
            ", legalName=" + legalName +
            ", legalMobile=" + legalMobile +
            ", consignorName=" + consignorName +
            ", consignorMobile=" + consignorMobile +
            ", isChange=" + isChange +
            ", agreementNo=" + agreementNo +
            ", belongUnitName=" + belongUnitName +
            ", tenantCreditCode=" + tenantCreditCode +
            ", delFlag=" + delFlag +
        "}";
    }
}
