package com.bonc.ioc.bzf.busisigning.config;

import com.bonc.ioc.bzf.busisigning.utils.ThreadLocalUtil;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * <b>@Desc</b>:   1、继承RequestInterceptor，把header设置到请求中,注意header的key若是大写时，请求中会被转为小写
 * <b>@Author</b>: hesh
 * <b>@Date</b>:   2020/6/21
 * <b>@Modify</b>:
 */
@Slf4j
@Configuration
public class FeignConfig implements RequestInterceptor {

    /**
     * 中心编号 常量
     */
    private static final String CENTER_CODE = "centerCode";

    /**
     * 中心编号
     */
    @Value("${projectCenter.centerCode}")
    private String projectCenterCode;

    @Override
    public void apply(RequestTemplate requestTemplate) {

        //读取设置的header信息，传递到下一个服务
        Map<String, String> headerMap = ThreadLocalUtil.get();

        // 请求头设置中心编号
        headerMap.put(CENTER_CODE, projectCenterCode);

        for (String key : headerMap.keySet()) {
            log.info("--从ThreadLocal获取消息头传递到下一个服务：key-[{}],value-[{}]",key,headerMap.get(key));
            requestTemplate.header(key,headerMap.get(key));
        }

    }
}
