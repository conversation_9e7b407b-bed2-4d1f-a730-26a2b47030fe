package com.bonc.ioc.bzf.busisigning.vo;

import com.bonc.ioc.common.base.vo.McpBasePageVo;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 消息模板信息表 实体类
 *
 * <AUTHOR>
 * @date 2022-08-03
 * @change 2022-08-03 by sqj for init
 */
@ApiModel(value = "MessTemplateInfoPageResultVo对象", description = "消息模板信息表")
public class MessTemplateInfoPageResultVo extends McpBasePageVo implements Serializable {


    @NotBlank(message = "不能为空", groups = {UpdateValidatorGroup.class})
    @ApiModelProperty(value = "消息模板id")
    private String messTemplateId;

    /**
     * 消息模板名称
     */
    @ApiModelProperty(value = "消息模板名称")
    private String messTemplateName;

    /**
     * 消息模板内容
     */
    @ApiModelProperty(value = "消息模板内容")
    private String messTemplateContent;


    /**
     * 删除标记 1=删除，0=未删除
     */
    @ApiModelProperty(value = "删除标记 1=删除，0=未删除")
    private Integer isDelete;


    @ApiModelProperty(value = "模板类型名称")
    private String templateTypeName;

    @ApiModelProperty(value = "创建人")
    private String createUser;

    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @ApiModelProperty(value = "修改人")
    private String modifyUser;

    @ApiModelProperty(value = "修改时间")
    private String modifyTime;

    /**
     * @return
     */
    public String getMessTemplateId() {
        return messTemplateId;
    }

    public void setMessTemplateId(String messTemplateId) {
        this.messTemplateId = messTemplateId;
    }

    /**
     * @return 消息模板名称
     */
    public String getMessTemplateName() {
        return messTemplateName;
    }

    public void setMessTemplateName(String messTemplateName) {
        this.messTemplateName = messTemplateName;
    }

    /**
     * @return 消息模板内容
     */
    public String getMessTemplateContent() {
        return messTemplateContent;
    }

    public void setMessTemplateContent(String messTemplateContent) {
        this.messTemplateContent = messTemplateContent;
    }

    /**
     * @return 删除标记 1=删除，0=未删除
     */
    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    /**
     * @return 模板类型名称
     */
    public String getTemplateTypeName() {
        return templateTypeName;
    }

    public void setTemplateTypeName(String templateTypeName) {
        this.templateTypeName = templateTypeName;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getModifyUser() {
        return modifyUser;
    }

    public void setModifyUser(String modifyUser) {
        this.modifyUser = modifyUser;
    }

    public String getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    @Override
    public String toString() {
        return "MessTemplateInfoPageResultVo{" +
                "messTemplateId=" + messTemplateId +
                ", messTemplateName=" + messTemplateName +
                ", messTemplateContent=" + messTemplateContent +
                ", isDelete=" + isDelete +
                ", templateTypeName=" + templateTypeName +
                "}";
    }
}
