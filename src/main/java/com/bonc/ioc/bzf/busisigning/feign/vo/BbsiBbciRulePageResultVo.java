package com.bonc.ioc.bzf.busisigning.feign.vo;

import com.bonc.ioc.common.base.vo.McpBasePageVo;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 签约入住规则表v2.0 实体类
 *
 * <AUTHOR>
 * @date 2023-06-13
 * @change 2023-06-13 by 宋鑫 for init
 */
@ApiModel(value="BbsiBbciRulePageResultVo对象", description="签约入住规则表v2.0")
public class BbsiBbciRulePageResultVo extends McpBasePageVo implements Serializable{


    /**
     * 规则ID
     */
    @ApiModelProperty(value = "规则ID")
    @NotBlank(message = "规则ID不能为空",groups = {UpdateValidatorGroup.class})
                                  private String ruleId;

    /**
     * 规则名称
     */
    @ApiModelProperty(value = "规则名称")
                            private String ruleName;

    /**
     * 计划ID
     */
    @ApiModelProperty(value = "计划ID")
                            private String planId;

    /**
     * 小区id
     */
    @ApiModelProperty(value = "小区id")
                            private String communityId;

    /**
     * 入住须知id
     */
    @ApiModelProperty(value = "入住须知id")
                            private String knowId;

    /**
     * 删除标识(1.未删除 0.已删除)
     */
    @ApiModelProperty(value = "删除标识(1.未删除 0.已删除)")
                            private String delFlag;

    /**
     * 房屋核验单id
     */
    @ApiModelProperty(value = "房屋核验单id")
                            private String houseCheckId;

    /**
     * 短信通知模板id
     */
    @ApiModelProperty(value = "短信通知模板id")
                            private String messageTemplateId;

    /**
     * 消息通知模板id
     */
    @ApiModelProperty(value = "消息通知模板id")
                            private String noticeTemplateId;

    /**
     * 签约计划id
     */
    @ApiModelProperty(value = "签约计划id")
                            private String signRuleId;

    /**
     * @return 规则ID
     */
    public String getRuleId() {
        return ruleId;
    }

    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
    }

    /**
     * @return 规则名称
     */
    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    /**
     * @return 计划ID
     */
    public String getPlanId() {
        return planId;
    }

    public void setPlanId(String planId) {
        this.planId = planId;
    }

    /**
     * @return 小区id
     */
    public String getCommunityId() {
        return communityId;
    }

    public void setCommunityId(String communityId) {
        this.communityId = communityId;
    }

    /**
     * @return 入住须知id
     */
    public String getKnowId() {
        return knowId;
    }

    public void setKnowId(String knowId) {
        this.knowId = knowId;
    }

    /**
     * @return 删除标识(1.未删除 0.已删除)
     */
    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    /**
     * @return 房屋核验单id
     */
    public String getHouseCheckId() {
        return houseCheckId;
    }

    public void setHouseCheckId(String houseCheckId) {
        this.houseCheckId = houseCheckId;
    }

    /**
     * @return 短信通知模板id
     */
    public String getMessageTemplateId() {
        return messageTemplateId;
    }

    public void setMessageTemplateId(String messageTemplateId) {
        this.messageTemplateId = messageTemplateId;
    }

    /**
     * @return 消息通知模板id
     */
    public String getNoticeTemplateId() {
        return noticeTemplateId;
    }

    public void setNoticeTemplateId(String noticeTemplateId) {
        this.noticeTemplateId = noticeTemplateId;
    }

    /**
     * @return 签约计划id
     */
    public String getSignRuleId() {
        return signRuleId;
    }

    public void setSignRuleId(String signRuleId) {
        this.signRuleId = signRuleId;
    }

      @Override
    public String toString() {
        return "BbsiBbciRulePageResultVo{" +
            "ruleId=" + ruleId +
            ", ruleName=" + ruleName +
            ", planId=" + planId +
            ", communityId=" + communityId +
            ", knowId=" + knowId +
            ", delFlag=" + delFlag +
            ", houseCheckId=" + houseCheckId +
            ", messageTemplateId=" + messageTemplateId +
            ", noticeTemplateId=" + noticeTemplateId +
            ", signRuleId=" + signRuleId +
        "}";
    }
}
