package com.bonc.ioc.bzf.busisigning.vo;

import com.bonc.ioc.common.base.vo.McpBasePageVo;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 导入记录表 实体类
 *
 * <AUTHOR>
 * @date 2024-03-07
 * @change 2024-03-07 by pyj for init
 */
@ApiModel(value="BbsImportRecordPageResultVo对象", description="导入记录表")
public class BbsImportRecordPageResultVo extends McpBasePageVo implements Serializable{


    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @NotBlank(message = "主键id不能为空",groups = {UpdateValidatorGroup.class})
                                  private String importId;

    /**
     * traceId
     */
    @ApiModelProperty(value = "traceId")
                            private String traceId;

    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
                            private String batchNo;

    /**
     * 导入关键字
     */
    @ApiModelProperty(value = "导入关键字")
                            private String importKey;

    /**
     * 是否成功(0.否 1.是)
     */
    @ApiModelProperty(value = "是否成功(0.否 1.是)")
                            private String importSuccess;

    /**
     * 导入参数
     */
    @ApiModelProperty(value = "导入参数")
                            private String importBody;

    /**
     * 导入结果
     */
    @ApiModelProperty(value = "导入结果")
                            private String importResult;

    /**
     * 导入异常
     */
    @ApiModelProperty(value = "导入异常")
                            private String importException;

    /**
     * @return 主键id
     */
    public String getImportId() {
        return importId;
    }

    public void setImportId(String importId) {
        this.importId = importId;
    }

    /**
     * @return traceId
     */
    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    /**
     * @return 批次号
     */
    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    /**
     * @return 导入关键字
     */
    public String getImportKey() {
        return importKey;
    }

    public void setImportKey(String importKey) {
        this.importKey = importKey;
    }

    /**
     * @return 是否成功(0.否 1.是)
     */
    public String getImportSuccess() {
        return importSuccess;
    }

    public void setImportSuccess(String importSuccess) {
        this.importSuccess = importSuccess;
    }

    /**
     * @return 导入参数
     */
    public String getImportBody() {
        return importBody;
    }

    public void setImportBody(String importBody) {
        this.importBody = importBody;
    }

    /**
     * @return 导入结果
     */
    public String getImportResult() {
        return importResult;
    }

    public void setImportResult(String importResult) {
        this.importResult = importResult;
    }

    /**
     * @return 导入异常
     */
    public String getImportException() {
        return importException;
    }

    public void setImportException(String importException) {
        this.importException = importException;
    }

      @Override
    public String toString() {
        return "BbsImportRecordPageResultVo{" +
            "importId=" + importId +
            ", traceId=" + traceId +
            ", batchNo=" + batchNo +
            ", importKey=" + importKey +
            ", importSuccess=" + importSuccess +
            ", importBody=" + importBody +
            ", importResult=" + importResult +
            ", importException=" + importException +
        "}";
    }
}
