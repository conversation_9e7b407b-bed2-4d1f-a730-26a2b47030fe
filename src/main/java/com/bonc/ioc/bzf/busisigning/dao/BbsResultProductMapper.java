package com.bonc.ioc.bzf.busisigning.dao;

import com.bonc.ioc.bzf.busisigning.entity.BbsResultProductEntity;
import com.bonc.ioc.bzf.busisigning.vo.BbsResultProductPageResultVo;
import com.bonc.ioc.bzf.busisigning.vo.BbsResultProductPageVo;
import com.bonc.ioc.bzf.busisigning.vo.ProductSignBasicInfoVo;
import com.bonc.ioc.common.base.mapper.McpBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 人-产品：产品表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2023-08-30
 * @change 2023-08-30 by liwenqiang for init
 */
@Mapper
public interface BbsResultProductMapper extends McpBaseMapper<BbsResultProductEntity> {
    /**
     * selectByPageCustom 分页查询
     *
     * @param vo 查询条件
     * @return 查询结果
     * @since 1.0.0
     * <AUTHOR>
     * @date  2023-08-30
     * @change 2023-08-30 by liwenqiang for init
     */
    List<BbsResultProductPageResultVo> selectByPageCustom(@Param("vo") BbsResultProductPageVo vo );

    List<ProductSignBasicInfoVo> getProductSignBasicInfo(String productNo);
    List<ProductSignBasicInfoVo> getProductSignBasicInfoList(List<String> productNo);

    List<BbsResultProductEntity> selectBySignId(@Param("signId") String signId);


    List<BbsResultProductEntity> selectRenewalBySignId(@Param("signId") String signId);

}
