package com.bonc.ioc.bzf.busisigning.feign.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value = "BbsSignAuditInfoVo对象", description = "签约审核信息")
@Data
public class BbsSignAuditInfoVo extends McpBaseVo implements Serializable {

    /**
     * 签约计划id(0.无计划)
     */
    @ApiModelProperty(value = "签约计划id(0.无计划)")
    private String planId;

    /**
     * 签约状态(1.暂存 2.待签约 3.已签约 4.未签约)
     */
    @ApiModelProperty(value = "签约状态(1.暂存 2.待签约 3.已签约 4.未签约)")
    private String signStatus;


    /**
     * 审批状态(1.通过 2.未通过 3.待审核 4.撤回)
     */
    @ApiModelProperty(value = "审批状态(1.通过 2.未通过 3.待审核 4.撤回)")
    private String approveStatus;
}