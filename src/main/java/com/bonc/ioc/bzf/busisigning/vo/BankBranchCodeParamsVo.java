package com.bonc.ioc.bzf.busisigning.vo;

import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> 获取开户行 参数实体
 * @version 1.0
 * @date 2022/12/13 9:41
 */
@Data
public class BankBranchCodeParamsVo {

	@ApiModelProperty(value = "项目id")
	@NotBlank(message = "项目id不能为空", groups = {UpdateValidatorGroup.class})
	private  String projectId;

	@ApiModelProperty(value = "分行银行名称")
	private  String bankBranchName;

	/**
	 * 页码
	 */
	@ApiModelProperty(value = "页码")
	@NotNull(message = "页码不能为空", groups = {UpdateValidatorGroup.class})
	private Integer current;

	/**
	 * 每页显示多少条
	 */
	@ApiModelProperty(value = "每页显示多少条")
	@NotNull(message = "每页显示多少条不能为空", groups = {UpdateValidatorGroup.class})
	private Integer size;

	/**
	 * 全量分页标识
	 */
	@ApiModelProperty(value = "全量分页标识")
	private String fullPage;
}
