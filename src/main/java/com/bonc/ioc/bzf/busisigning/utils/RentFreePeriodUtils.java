package com.bonc.ioc.bzf.busisigning.utils;

import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.bonc.ioc.bzf.busisigning.dao.BbsSignFpIntervalMapper;
import com.bonc.ioc.bzf.busisigning.dao.BbsSignIncrementalConfigMapper;
import com.bonc.ioc.bzf.busisigning.entity.BbsResultProductEntity;
import com.bonc.ioc.bzf.busisigning.entity.BbsSignFpIntervalEntity;
import com.bonc.ioc.bzf.busisigning.entity.BbsSignIncrementalConfigEntity;
import com.bonc.ioc.bzf.busisigning.entity.BbsSignInfoEntity;
import com.bonc.ioc.bzf.busisigning.enums.AreaTypeEnum;
import com.bonc.ioc.bzf.busisigning.enums.DelFlagEnum;
import com.bonc.ioc.bzf.busisigning.vo.JsonVo.*;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: liwenqiang
 * @createDate: 2023-09-01 10:28
 * @Version 1.0
 * 免租期工具类
 **/
public class RentFreePeriodUtils {
    /**
     * 获取免租期区间
     * @param kind 0-租金 1-物业费
     * */
    public static String getRentFree(String kind, BbsSignInfoEntity signInfoEntity, BbsSignFpIntervalMapper fpIntervalMapper){
        if ("0".equals(kind)){
            if ("0".equals(signInfoEntity.getRentFreePeriodType())){
                return "--";
            }else if ("2".equals(signInfoEntity.getRentFreePeriodType())){
                return signInfoEntity.getRentFpFixedValue()+"天";
            }else {
                String rentFree = new LambdaQueryChainWrapper<>(fpIntervalMapper)
                        .eq(BbsSignFpIntervalEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                        .eq(BbsSignFpIntervalEntity::getStandardType, "rent")
                        .eq(BbsSignFpIntervalEntity::getSignInfoId, signInfoEntity.getSignId())
                        .list().stream().map(en -> en.getStart() + "~" + en.getEnd()).collect(Collectors.joining(","));
                return rentFree;
            }
        }else {
            if ("0".equals(signInfoEntity.getPropFreePeriodType())){
                return "--";
            }else if ("2".equals(signInfoEntity.getPropFreePeriodType())){
                return signInfoEntity.getPropFpFixedValue()+"天";
            }else {
                String rentFree = new LambdaQueryChainWrapper<>(fpIntervalMapper)
                        .eq(BbsSignFpIntervalEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                        .eq(BbsSignFpIntervalEntity::getStandardType, "prop")
                        .eq(BbsSignFpIntervalEntity::getSignInfoId, signInfoEntity.getSignId())
                        .list().stream().map(en -> en.getStart() + "~" + en.getEnd()).collect(Collectors.joining(","));
                return rentFree;
            }
        }
    }
    /**
     * 获取租金递增
     * @param type 0-租金 1-物业费
     * */
    public static List<IncrementalInfoArray> getIncremental(String type, String signId, BbsSignIncrementalConfigMapper incrementalConfigMapper){
        List<IncrementalInfoArray> list=new ArrayList<>();
        if ("租金".equals(type)){
            List<BbsSignIncrementalConfigEntity> incrementalConfigEntities = new LambdaQueryChainWrapper<>(incrementalConfigMapper)
                    .eq(BbsSignIncrementalConfigEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                    .eq(BbsSignIncrementalConfigEntity::getSignInfoId, signId)
                    .eq(BbsSignIncrementalConfigEntity::getStandardType, "rent")
                    .list().stream().sorted(Comparator.comparing(BbsSignIncrementalConfigEntity::getCreateTime)).collect(Collectors.toList());
            incrementalConfigEntities.forEach(item->{
                IncrementalInfoArray array=new IncrementalInfoArray();
                BeanUtils.copyProperties(item,array);
                list.add(array);
            });
            return list;
        }else {
            List<BbsSignIncrementalConfigEntity> incrementalConfigEntities =new LambdaQueryChainWrapper<>(incrementalConfigMapper)
                    .eq(BbsSignIncrementalConfigEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                    .eq(BbsSignIncrementalConfigEntity::getSignInfoId, signId)
                    .eq(BbsSignIncrementalConfigEntity::getStandardType, "prop")
                    .list().stream().sorted(Comparator.comparing(BbsSignIncrementalConfigEntity::getCreateTime)).collect(Collectors.toList());
            incrementalConfigEntities.forEach(item->{
                IncrementalInfoArray array=new IncrementalInfoArray();
                BeanUtils.copyProperties(item,array);
                list.add(array);
            });
            return list;
        }
    }
    /**
     * 拼接租金json
     * */
    public static JsonRootBean getRentStandardJson(BbsSignInfoEntity signInfoEntity, List<BbsResultProductEntity> productEntityList ,
                                             BbsSignFpIntervalMapper fpIntervalMapper,BbsSignIncrementalConfigMapper incrementalConfigMapper){
        JsonRootBean jsonRootBean=new JsonRootBean();
        StandardInfo standardInfo=new StandardInfo();//租金标准
        standardInfo.setTaxRate(Objects.nonNull(signInfoEntity.getRentTaxRate())?signInfoEntity.getRentTaxRate().toString():null);//税率
        standardInfo.setProductInfoArray(getProductInfoArray("租金", signInfoEntity.getAreaType(), productEntityList));//租金标准
        RentFreePeriodType rentFreePeriodType=new RentFreePeriodType();//免租期
        rentFreePeriodType.setType(signInfoEntity.getRentFreePeriodType());
        FixedValueInfo fixedValueInfo=new FixedValueInfo();
        fixedValueInfo.setFixDate(signInfoEntity.getRentFpFixedDate());
        fixedValueInfo.setValue(signInfoEntity.getRentFpFixedValue());
        rentFreePeriodType.setFixedValueInfo(fixedValueInfo);
        List<IntervalArray> intervalArray=new ArrayList<>();
        List<BbsSignFpIntervalEntity> fpIntervalEntities = new LambdaQueryChainWrapper<>(fpIntervalMapper)
                .eq(BbsSignFpIntervalEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsSignFpIntervalEntity::getStandardType, "rent")
                .eq(BbsSignFpIntervalEntity::getSignInfoId, signInfoEntity.getSignId())
                .list();
        fpIntervalEntities.forEach(item->{
            IntervalArray interval=new IntervalArray();
            interval.setStart(item.getStart());
            interval.setEnd(item.getEnd());
            intervalArray.add(interval);
        });
        rentFreePeriodType.setIntervalArray(intervalArray);
        IncrementalInfo incrementalInfo=new IncrementalInfo();
        incrementalInfo.setIncrementalFlag(signInfoEntity.getRentIncrementalFlag());
        incrementalInfo.setIncrementalType(signInfoEntity.getRentIncrementalType());
        if ("1".equals(signInfoEntity.getRentIncrementalFlag())){
            incrementalInfo.setIncrementalInfoArray(RentFreePeriodUtils.getIncremental("租金", signInfoEntity.getSignId(), incrementalConfigMapper));
        }
        jsonRootBean.setStandardInfo(standardInfo);
        jsonRootBean.setRentFreePeriodType(rentFreePeriodType);
        jsonRootBean.setIncrementalInfo(incrementalInfo);
        return jsonRootBean;
    }
    /**
     * 拼接物业费租金json
     * */
    public static JsonRootBean getPropStandardJson(BbsSignInfoEntity signInfoEntity, List<BbsResultProductEntity> productEntityList ,
                                             BbsSignFpIntervalMapper fpIntervalMapper,BbsSignIncrementalConfigMapper incrementalConfigMapper){
        JsonRootBean jsonRootBean=new JsonRootBean();
        StandardInfo standardInfo=new StandardInfo();//租金标准
        standardInfo.setTaxRate(Objects.nonNull(signInfoEntity.getPropTaxRate())?signInfoEntity.getPropTaxRate().toString():null);//税率
        standardInfo.setProductInfoArray(getProductInfoArray("物业费", signInfoEntity.getAreaType(), productEntityList));//租金标准
        RentFreePeriodType rentFreePeriodType=new RentFreePeriodType();//免租期
        rentFreePeriodType.setType(signInfoEntity.getPropFreePeriodType());
        FixedValueInfo fixedValueInfo=new FixedValueInfo();
        fixedValueInfo.setFixDate(signInfoEntity.getPropFpFixedDate());
        fixedValueInfo.setValue(signInfoEntity.getPropFpFixedValue());
        rentFreePeriodType.setFixedValueInfo(fixedValueInfo);
        List<IntervalArray> intervalArray=new ArrayList<>();
        List<BbsSignFpIntervalEntity> fpIntervalEntities = new LambdaQueryChainWrapper<>(fpIntervalMapper)
                .eq(BbsSignFpIntervalEntity::getDelFlag, DelFlagEnum.UNDELETED.getCode())
                .eq(BbsSignFpIntervalEntity::getStandardType, "prop")
                .eq(BbsSignFpIntervalEntity::getSignInfoId, signInfoEntity.getSignId())
                .list();
        fpIntervalEntities.forEach(item->{
            IntervalArray interval=new IntervalArray();
            interval.setStart(item.getStart());
            interval.setEnd(item.getEnd());
            intervalArray.add(interval);
        });
        rentFreePeriodType.setIntervalArray(intervalArray);
        IncrementalInfo incrementalInfo=new IncrementalInfo();
        incrementalInfo.setIncrementalFlag(signInfoEntity.getPropIncrementalFlag());
        incrementalInfo.setIncrementalType(signInfoEntity.getPropIncrementalType());
        if ("1".equals(signInfoEntity.getPropIncrementalFlag())){
            incrementalInfo.setIncrementalInfoArray(RentFreePeriodUtils.getIncremental("物业费", signInfoEntity.getSignId(), incrementalConfigMapper));
        }
        jsonRootBean.setStandardInfo(standardInfo);
        jsonRootBean.setRentFreePeriodType(rentFreePeriodType);
        jsonRootBean.setIncrementalInfo(incrementalInfo);
        return jsonRootBean;
    }
    public static List<ProductInfoArray> getProductInfoArray(String type,
                                                             String areaType,
                                                             List<BbsResultProductEntity> productEntityList){
        List<ProductInfoArray> arrayList=new ArrayList<>();
        //租金
        if ("租金".equals(type)){
            productEntityList.forEach(item->{
                ProductInfoArray productInfoArray=new ProductInfoArray();
                productInfoArray.setProductNo(item.getProductNo());
                productInfoArray.setProductName(item.getProductName());
                productInfoArray.setStandardUnit(item.getRentStandardUnit());
                productInfoArray.setStandardUnitName(item.getRentStandardUnitName());
                productInfoArray.setStandardValue(Objects.nonNull(item.getRentStandard())?item.getRentStandard().toString():null);
                productInfoArray.setChargeRuleCode(item.getMonthlyRentRulesNo());
                productInfoArray.setChargeRuleName(item.getMonthlyRentRulesName());
                productInfoArray.setArea(AreaTypeEnum.INNER_AREA_TYPE.getCode().equals(areaType)? item.getInnerSleeveArea() : item.getHouseStructArea());
                arrayList.add(productInfoArray);
            });
        }else {
            productEntityList.forEach(item->{
                ProductInfoArray productInfoArray=new ProductInfoArray();
                productInfoArray.setProductNo(item.getProductNo());
                productInfoArray.setProductName(item.getProductName());
                productInfoArray.setStandardUnit(item.getPropStandardUnit());
                    productInfoArray.setStandardUnitName(item.getPropStandardUnitName());
                productInfoArray.setStandardValue(Convert.toStr(item.getPropStandard()));
                productInfoArray.setChargeRuleCode(item.getMonthlyPropRulesNo());
                productInfoArray.setChargeRuleName(item.getMonthlyPropRulesName());
                productInfoArray.setArea(AreaTypeEnum.INNER_AREA_TYPE.getCode().equals(areaType)? item.getInnerSleeveArea() : item.getHouseStructArea());
                arrayList.add(productInfoArray);
            });
        }
        return arrayList;
    }

}
