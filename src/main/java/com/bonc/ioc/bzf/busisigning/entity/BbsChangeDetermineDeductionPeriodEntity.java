package com.bonc.ioc.bzf.busisigning.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.bonc.ioc.common.base.entity.McpBaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import com.bonc.ioc.common.validator.inf.*;
import javax.validation.constraints.*;

/**
 * 确定的抵扣账期 实体类
 *
 * <AUTHOR>
 * @date 2024-10-30
 * @change 2024-10-30 by tbh for init
 */
@TableName("bbs_change_determine_deduction_period")
@ApiModel(value="BbsChangeDetermineDeductionPeriodEntity对象", description="确定的抵扣账期")
public class BbsChangeDetermineDeductionPeriodEntity extends McpBaseEntity implements Serializable{

    public static final String FIELD_PERIOD_ID = "period_id";
    public static final String FIELD_CC_ID = "cc_id";
    public static final String FIELD_BILL_ID = "bill_id";
    public static final String FIELD_TYPE = "type";
    public static final String FIELD_DEDUCTION_AMOUNT = "deduction_amount";
    public static final String FIELD_PAID_IN_MONEY = "paid_in_money";
    public static final String FIELD_CHARGE_PERIOD = "charge_period";
    public static final String FIELD_CHARGE_START_DATE = "charge_start_date";
    public static final String FIELD_CHARGE_END_DATE = "charge_end_date";
    public static final String FIELD_PARAMPRICE = "paramprice";
    public static final String FIELD_PARAMAREA = "paramarea";
    public static final String FIELD_MONTHS = "months";
    public static final String FIELD_DAYS = "days";
    public static final String FIELD_PAYABLE_MONEY = "payable_money";
    public static final String FIELD_TAX_RATE = "tax_rate";
    public static final String FIELD_EXCLUDING_RATE_MONEY = "excluding_rate_money";
    public static final String FIELD_RATE_MONEY = "rate_money";
    public static final String FIELD_DAY_MONEY = "day_money";
    public static final String FIELD_HOUSE_NAME = "house_name";
    public static final String FIELD_HOUSE_ID = "house_id";
    public static final String FIELD_PAYABLE_DATE = "payable_date";
    public static final String FIELD_TO_BE_PAID_MONEY = "to_be_paid_money";
    public static final String FIELD_CHARGE_CYCLE_STR = "charge_cycle_str";
    public static final String FIELD_PAYMENT_STATUS_STR = "payment_status_str";
    public static final String FIELD_CHARGE_STATUS_STR = "charge_status_str";
    public static final String FIELD_STATUS_STR = "status_str";

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
                                @TableId(value = "period_id", type = IdType.ASSIGN_UUID)
                                  private String periodId;

    /**
     * 合同变更表主键id
     */
    @ApiModelProperty(value = "合同变更表主键id")
                            private String ccId;

    /**
     * 预览账单表主键id(备用)
     */
    @ApiModelProperty(value = "预览账单表主键id(备用)")
                            private String billId;

    @ApiModelProperty(value = "1租金押金抵扣,3物业费抵扣")
                            private String type;

    /**
     * 抵扣金额
     */
    @ApiModelProperty(value = "抵扣金额")
                            private String deductionAmount;

    /**
     * (已缴金额)实际收款金额
     */
    @ApiModelProperty(value = "(已缴金额)实际收款金额")
                            private String paidInMoney;

    /**
     * 缴费次数
     */
    @ApiModelProperty(value = "缴费次数")
                            private String chargePeriod;

    /**
     * 缴费开始时间
     */
    @ApiModelProperty(value = "缴费开始时间")
            @JsonFormat(pattern = "yyyy-MM-dd")
                    private Date chargeStartDate;

    /**
     * 缴费结束时间
     */
    @ApiModelProperty(value = "缴费结束时间")
            @JsonFormat(pattern = "yyyy-MM-dd")
                    private Date chargeEndDate;

    /**
     * 租金单价
     */
    @ApiModelProperty(value = "租金单价")
                            private String paramprice;

    /**
     * 面积
     */
    @ApiModelProperty(value = "面积")
                            private String paramarea;

    /**
     * 月数
     */
    @ApiModelProperty(value = "月数")
                            private String months;

    /**
     * 天数
     */
    @ApiModelProperty(value = "天数")
                            private String days;

    /**
     * 应缴金额
     */
    @ApiModelProperty(value = "应缴金额")
                            private String payableMoney;

    /**
     * 增值税率
     */
    @ApiModelProperty(value = "增值税率")
                            private String taxRate;

    /**
     * 不含税金额
     */
    @ApiModelProperty(value = "不含税金额")
                            private String excludingRateMoney;

    /**
     * 增值税额
     */
    @ApiModelProperty(value = "增值税额")
                            private String rateMoney;

    /**
     * 日租金
     */
    @ApiModelProperty(value = "日租金")
                            private String dayMoney;

    /**
     * 房源名称
     */
    @ApiModelProperty(value = "房源名称")
                            private String houseName;

    /**
     * 房屋ID
     */
    @ApiModelProperty(value = "房屋ID")
                            private String houseId;

    /**
     * 应缴日期
     */
    @ApiModelProperty(value = "应缴日期")
            @JsonFormat(pattern = "yyyy-MM-dd")
                    private Date payableDate;

    /**
     * 待缴金额
     */
    @ApiModelProperty(value = "待缴金额")
                            private String toBePaidMoney;

    /**
     * 收费周期
     */
    @ApiModelProperty(value = "收费周期")
                            private String chargeCycleStr;

    /**
     * 支付状态
     */
    @ApiModelProperty(value = "支付状态")
                            private String paymentStatusStr;

    /**
     * 缴费状态
     */
    @ApiModelProperty(value = "缴费状态")
                            private String chargeStatusStr;

    /**
     * 账单状态
     */
    @ApiModelProperty(value = "账单状态")
                            private String statusStr;

    @ApiModelProperty(value = "月租金")
    private String monthAmount;
    @ApiModelProperty(value = "备注(工银实际没有这个东西)")
    private String notes;

    @ApiModelProperty(value = "计费科目")
    private String chargeSubjectNo;	//计费科目String 是 (01租金 02押金 07物业费)

    public String getChargeSubjectNo() {
        return chargeSubjectNo;
    }

    public void setChargeSubjectNo(String chargeSubjectNo) {
        this.chargeSubjectNo = chargeSubjectNo;
    }

    public String getMonthAmount() {
        return monthAmount;
    }

    public void setMonthAmount(String monthAmount) {
        this.monthAmount = monthAmount;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    /**
     * @return 主键
     */
    public String getPeriodId() {
        return periodId;
    }

    public void setPeriodId(String periodId) {
        this.periodId = periodId;
    }

    /**
     * @return 合同变更表主键id
     */
    public String getCcId() {
        return ccId;
    }

    public void setCcId(String ccId) {
        this.ccId = ccId;
    }

    /**
     * @return 预览账单表主键id(备用)
     */
    public String getBillId() {
        return billId;
    }

    public void setBillId(String billId) {
        this.billId = billId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    /**
     * @return 抵扣金额
     */
    public String getDeductionAmount() {
        return deductionAmount;
    }

    public void setDeductionAmount(String deductionAmount) {
        this.deductionAmount = deductionAmount;
    }

    /**
     * @return (已缴金额)实际收款金额
     */
    public String getPaidInMoney() {
        return paidInMoney;
    }

    public void setPaidInMoney(String paidInMoney) {
        this.paidInMoney = paidInMoney;
    }

    /**
     * @return 缴费次数
     */
    public String getChargePeriod() {
        return chargePeriod;
    }

    public void setChargePeriod(String chargePeriod) {
        this.chargePeriod = chargePeriod;
    }

    /**
     * @return 缴费开始时间
     */
    public Date getChargeStartDate(){
        if(chargeStartDate!=null){
            return (Date)chargeStartDate.clone();
        }else{
            return null;
        }
    }

    public void setChargeStartDate(Date chargeStartDate) {
        if(chargeStartDate==null){
            this.chargeStartDate = null;
        }else{
            this.chargeStartDate = (Date)chargeStartDate.clone();
        }
    }

    /**
     * @return 缴费结束时间
     */
    public Date getChargeEndDate(){
        if(chargeEndDate!=null){
            return (Date)chargeEndDate.clone();
        }else{
            return null;
        }
    }

    public void setChargeEndDate(Date chargeEndDate) {
        if(chargeEndDate==null){
            this.chargeEndDate = null;
        }else{
            this.chargeEndDate = (Date)chargeEndDate.clone();
        }
    }

    /**
     * @return 租金单价
     */
    public String getParamprice() {
        return paramprice;
    }

    public void setParamprice(String paramprice) {
        this.paramprice = paramprice;
    }

    /**
     * @return 面积
     */
    public String getParamarea() {
        return paramarea;
    }

    public void setParamarea(String paramarea) {
        this.paramarea = paramarea;
    }

    /**
     * @return 月数
     */
    public String getMonths() {
        return months;
    }

    public void setMonths(String months) {
        this.months = months;
    }

    /**
     * @return 天数
     */
    public String getDays() {
        return days;
    }

    public void setDays(String days) {
        this.days = days;
    }

    /**
     * @return 应缴金额
     */
    public String getPayableMoney() {
        return payableMoney;
    }

    public void setPayableMoney(String payableMoney) {
        this.payableMoney = payableMoney;
    }

    /**
     * @return 增值税率
     */
    public String getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(String taxRate) {
        this.taxRate = taxRate;
    }

    /**
     * @return 不含税金额
     */
    public String getExcludingRateMoney() {
        return excludingRateMoney;
    }

    public void setExcludingRateMoney(String excludingRateMoney) {
        this.excludingRateMoney = excludingRateMoney;
    }

    /**
     * @return 增值税额
     */
    public String getRateMoney() {
        return rateMoney;
    }

    public void setRateMoney(String rateMoney) {
        this.rateMoney = rateMoney;
    }

    /**
     * @return 日租金
     */
    public String getDayMoney() {
        return dayMoney;
    }

    public void setDayMoney(String dayMoney) {
        this.dayMoney = dayMoney;
    }

    /**
     * @return 房源名称
     */
    public String getHouseName() {
        return houseName;
    }

    public void setHouseName(String houseName) {
        this.houseName = houseName;
    }

    /**
     * @return 房屋ID
     */
    public String getHouseId() {
        return houseId;
    }

    public void setHouseId(String houseId) {
        this.houseId = houseId;
    }

    /**
     * @return 应缴日期
     */
    public Date getPayableDate(){
        if(payableDate!=null){
            return (Date)payableDate.clone();
        }else{
            return null;
        }
    }

    public void setPayableDate(Date payableDate) {
        if(payableDate==null){
            this.payableDate = null;
        }else{
            this.payableDate = (Date)payableDate.clone();
        }
    }

    /**
     * @return 待缴金额
     */
    public String getToBePaidMoney() {
        return toBePaidMoney;
    }

    public void setToBePaidMoney(String toBePaidMoney) {
        this.toBePaidMoney = toBePaidMoney;
    }

    /**
     * @return 收费周期
     */
    public String getChargeCycleStr() {
        return chargeCycleStr;
    }

    public void setChargeCycleStr(String chargeCycleStr) {
        this.chargeCycleStr = chargeCycleStr;
    }

    /**
     * @return 支付状态
     */
    public String getPaymentStatusStr() {
        return paymentStatusStr;
    }

    public void setPaymentStatusStr(String paymentStatusStr) {
        this.paymentStatusStr = paymentStatusStr;
    }

    /**
     * @return 缴费状态
     */
    public String getChargeStatusStr() {
        return chargeStatusStr;
    }

    public void setChargeStatusStr(String chargeStatusStr) {
        this.chargeStatusStr = chargeStatusStr;
    }

    /**
     * @return 账单状态
     */
    public String getStatusStr() {
        return statusStr;
    }

    public void setStatusStr(String statusStr) {
        this.statusStr = statusStr;
    }

      @Override
    public String toString() {
        return "BbsChangeDetermineDeductionPeriodEntity{" +
            "periodId=" + periodId +
            ", ccId=" + ccId +
            ", billId=" + billId +
            ", type=" + type +
            ", deductionAmount=" + deductionAmount +
            ", paidInMoney=" + paidInMoney +
            ", chargePeriod=" + chargePeriod +
            ", chargeStartDate=" + chargeStartDate +
            ", chargeEndDate=" + chargeEndDate +
            ", paramprice=" + paramprice +
            ", paramarea=" + paramarea +
            ", months=" + months +
            ", days=" + days +
            ", payableMoney=" + payableMoney +
            ", taxRate=" + taxRate +
            ", excludingRateMoney=" + excludingRateMoney +
            ", rateMoney=" + rateMoney +
            ", dayMoney=" + dayMoney +
            ", houseName=" + houseName +
            ", houseId=" + houseId +
            ", payableDate=" + payableDate +
            ", toBePaidMoney=" + toBePaidMoney +
            ", chargeCycleStr=" + chargeCycleStr +
            ", paymentStatusStr=" + paymentStatusStr +
            ", chargeStatusStr=" + chargeStatusStr +
            ", statusStr=" + statusStr +
        "}";
    }
}