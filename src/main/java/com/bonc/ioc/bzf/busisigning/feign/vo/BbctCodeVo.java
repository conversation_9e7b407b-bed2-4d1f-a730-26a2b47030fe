package com.bonc.ioc.bzf.busisigning.feign.vo;

import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 编号 vo实体类
 *
 * <AUTHOR>
 * @since 2023/5/15
 */
@Data
@ApiModel(value = "编号 vo实体", description = "编号 vo实体")
public class BbctCodeVo extends BbctStatusVo implements Serializable {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @NotBlank(message = "主键id不能为空", groups = {
            UpdateValidatorGroup.class
    })
    private String id;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    @NotBlank(message = "编号不能为空", groups = {
            UpdateValidatorGroup.class
    })
    private String code;
}
