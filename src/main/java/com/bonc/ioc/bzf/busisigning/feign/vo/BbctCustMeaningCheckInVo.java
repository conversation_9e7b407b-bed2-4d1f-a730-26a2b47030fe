package com.bonc.ioc.bzf.busisigning.feign.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 同时办理入住 实体类
 *
 * <AUTHOR>
 * @since 2023/6/7
 */
@Data
@ApiModel(value = "BbctCustMeaningCheckInVo", description = "入住表")
public class BbctCustMeaningCheckInVo extends McpBaseVo implements Serializable {

    /**
     * 签约计划id
     */
    @ApiModelProperty(value = "签约计划id")
    private String signingPlanId;

    /**
     * 人房信息
     */
    @ApiModelProperty(value = "人房信息")
    private List<BbctCustCheckInVo> voList;

}
