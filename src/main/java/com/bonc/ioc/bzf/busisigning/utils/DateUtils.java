/**
 *
 */
package com.bonc.ioc.bzf.busisigning.utils;

import com.bonc.ioc.common.exception.McpException;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.Objects;

/**
 * 日期工具类, 继承org.apache.commons.lang.time.DateUtils类
 */
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {

    private static String[] parsePatterns = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};

    /**
     * 得到当前日期字符串 格式（yyyy-MM-dd）
     */
    public static String getDate() {
        return getDate("yyyy-MM-dd");
    }

    /**
     * 得到当前日期字符串 格式（yyyy-MM-dd） pattern可以为："yyyy-MM-dd" "HH:mm:ss" "E"
     */
    public static String getDate(String pattern) {
        return DateFormatUtils.format(new Date(), pattern);
    }

    /**
     * 得到日期字符串 默认格式（yyyy-MM-dd） pattern可以为："yyyy-MM-dd" "HH:mm:ss" "E"
     */
    public static String formatDate(Date date, Object... pattern) {
        if (ObjectUtils.isEmpty(date)) {
            return null;
        }
        String formatDate = null;
        if (pattern != null && pattern.length > 0) {
            formatDate = DateFormatUtils.format(date, pattern[0].toString());
        } else {
            formatDate = DateFormatUtils.format(date, "yyyy-MM-dd");
        }
        return formatDate;
    }

    /**
     * 得到日期时间字符串，转换格式（yyyy-MM-dd HH:mm:ss）
     */
    public static String formatDateTime(Date date) {
        return formatDate(date, "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 得到当前时间字符串 格式（HH:mm:ss）
     */
    public static String getTime() {
        return formatDate(new Date(), "HH:mm:ss");
    }

    /**
     * 得到当前日期和时间字符串 格式（yyyy-MM-dd HH:mm:ss）
     */
    public static String getDateTime() {
        return formatDate(new Date(), "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 得到当前年份字符串 格式（yyyy）
     */
    public static String getYear() {
        return formatDate(new Date(), "yyyy");
    }

    /**
     * 得到当前月份字符串 格式（MM）
     */
    public static String getMonth() {
        return formatDate(new Date(), "MM");
    }

    /**
     * 得到当天字符串 格式（dd）
     */
    public static String getDay() {
        return formatDate(new Date(), "dd");
    }
    public static Integer getDateYear(Date date){
        return Integer.valueOf(formatDate(date,"yyyy"));
    }
    public static Integer getDateMonth(Date date){
        return Integer.valueOf(formatDate(date, "MM"));
    }
    public static Integer getDateDay(Date date){
        return Integer.valueOf(formatDate(date, "dd"));
    }

    /**
     * 得到当前星期字符串 格式（E）星期几
     */
    public static String getWeek() {
        return formatDate(new Date(), "E");
    }

    /**
     * 日期型字符串转化为日期 格式
     * { "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm",
     * "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm",
     * "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm" }
     */
    public static Date parseDate(Object str) {
        if (str == null) {
            return null;
        }
        try {
            return parseDate(str.toString(), parsePatterns);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 获取过去的天数
     *
     * @param date
     * @return
     */
    public static long pastDays(Date date) {
        long t = new Date().getTime() - date.getTime();
        return t / (24 * 60 * 60 * 1000);
    }

    /**
     * 获取过去的小时
     *
     * @param date
     * @return
     */
    public static long pastHour(Date date) {
        long t = new Date().getTime() - date.getTime();
        return t / (60 * 60 * 1000);
    }

    /**
     * 获取过去的分钟
     *
     * @param date
     * @return
     */
    public static long pastMinutes(Date date) {
        long t = new Date().getTime() - date.getTime();
        return t / (60 * 1000);
    }

    /**
     * 转换为时间（天,时:分:秒.毫秒）
     *
     * @param timeMillis
     * @return
     */
    public static String formatDateTime(long timeMillis) {
        long day = timeMillis / (24 * 60 * 60 * 1000);
        long hour = (timeMillis / (60 * 60 * 1000) - day * 24);
        long min = ((timeMillis / (60 * 1000)) - day * 24 * 60 - hour * 60);
        long s = (timeMillis / 1000 - day * 24 * 60 * 60 - hour * 60 * 60 - min * 60);
        long sss = (timeMillis - day * 24 * 60 * 60 * 1000 - hour * 60 * 60 * 1000 - min * 60 * 1000 - s * 1000);
        return (day > 0 ? day + "," : "") + hour + ":" + min + ":" + s + "." + sss;
    }

    /**
     * 获取两个日期之间的天数
     *
     * @param before
     * @param after
     * @return
     */
    public static double getDistanceOfTwoDate(Date before, Date after) {
        long beforeTime = before.getTime();
        long afterTime = after.getTime();
        return (afterTime - beforeTime) / (1000 * 60 * 60 * 24);
    }

    /**
     * 获取两个日期相差多少个月
     */

    public static int getMonthSpace(String date1, String date2)
            throws ParseException {

        int result = 0;

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        Calendar c1 = Calendar.getInstance();
        Calendar c2 = Calendar.getInstance();

        c1.setTime(sdf.parse(date1));
        c2.setTime(sdf.parse(date2));

        int i = c2.get(Calendar.YEAR)-c1.get(Calendar.YEAR);
        int month = 0;
        if (i<0)
        {
            month = -i*12;
        }else if(i>0)
        {
            month =  i*12;
        }
        result = (c2.get(Calendar.MONDAY) - c1.get(Calendar.MONTH)) + month;

        return result == 0 ? 1 : Math.abs(result);

    }



    /**
     * 获取两个时间的月份差
     */
    public static Integer getDateDifference(Date startDate, Date endDate) {
        Integer startYear = getDateYear(startDate);
        Integer startMonth = getDateMonth(startDate);
        Integer startDay = getDateDay(startDate);
        Integer endYear = getDateYear(endDate);
        Integer endMonth = getDateMonth(endDate);
        Integer endDay = getDateDay(endDate);
        LocalDate startLocalDate = LocalDate.of(startYear, startMonth, startDay);
        LocalDate endLocalDate = LocalDate.of(endYear, endMonth, endDay);
        Period period = Period.between(startLocalDate, endLocalDate).normalized();
        int months = period.getYears() * 12 + period.getMonths();
        if (period.getDays() > 0) {
            months++;
        }
        return months;
    }
    /**
     * 获取某个时间加上天数的日期
     *
    public static String getDateAddDay(String date, Integer day){
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date parse = null;
        try {
            parse = dateFormat.parse(date);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        Integer dateYear = getDateYear(parse);
        Integer dateMonth = getDateMonth(parse);
        Integer dateDay = getDateDay(parse);
        LocalDate localDate = LocalDate.of(dateYear, dateMonth, dateDay);
        LocalDate plusDays = localDate.plusDays(day);
        return plusDays.toString();
    }*/

    /**
     * 获取某个时间加上天数的日期
     * */
    public static String getDateAddDaySubtraction(String date, Integer day){
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Date parse = dateFormat.parse(date);
            LocalDate localDate = parse.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate plusDays = localDate.plusDays(day - 1);
            return plusDays.toString();
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 获取当前日期过去几天的日期
     * @param date 当前日期
     * @param day 过去几天
     * @return
     */
    public static Date lastFewDays(Date date, int day) {
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return lastFewDays(localDate, day);
    }

    /**
     * 获取当前日期过去几天的日期
     * @param date 当前日期
     * @param day 过去几天
     * @return
     */
    public static Date lastFewDays(LocalDate date, int day) {
        LocalDate weekDayDate = date.minusDays(day);
        return Date.from(weekDayDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取当前日期后几天的日期
     * @param date 当前日期
     * @param day 后几天
     * @return
     */
    public static Date nextFewDays(Date date, int day) {
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return nextFewDays(localDate, day);
    }

    /**
     * 获取当前日期后几天的日期
     * @param date 当前日期
     * @param day 后几天
     * @return
     */
    public static Date nextFewDays(LocalDate date, int day) {
        LocalDate weekDayDate = date.plusDays(day);
        return Date.from(weekDayDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 大于
     * @param first 第一个参数
     * @param second 第二个参数
     * @return 是否大于
     */
    public static boolean greater(Date first, Date second) {
        return first.compareTo(second) > 0;
    }

    /**
     * 大于(日期)
     * @param first 第一个参数
     * @param second 第二个参数
     * @return 是否大于
     */
    public static boolean greaterByDay(Date first, Date second) {
        try {
            String fristStr = formatDate(first);
            first = new SimpleDateFormat("yyyy-MM-dd").parse(fristStr);

            String secondStr = formatDate(second);
            second = new SimpleDateFormat("yyyy-MM-dd").parse(secondStr);

            return first.compareTo(second) > 0;
        } catch (Exception e) {
            throw new McpException("解析日期失败");
        }
    }

    /**
     * 大于等于(日期)
     * @param first 第一个参数
     * @param second 第二个参数
     * @return 是否大于等于
     */
    public static boolean greaterOrEqualByDay(Date first, Date second) {
        try {
            String fristStr = formatDate(first);
            first = new SimpleDateFormat("yyyy-MM-dd").parse(fristStr);

            String secondStr = formatDate(second);
            second = new SimpleDateFormat("yyyy-MM-dd").parse(secondStr);

            return first.compareTo(second) >= 0;
        } catch (Exception e) {
            throw new McpException("解析日期失败");
        }
    }

    /**
     * 获取两个日期间的整数年,非整年时返回/
     * @param beginDate
     * @param endDate
     * @return
     */
    public static String getIntegerYearStr(Date beginDate, Date endDate) {
        Date tempDate=beginDate;
        SimpleDateFormat daySdf=new SimpleDateFormat("yyyyMMdd");
        int monthNum=0;
        boolean notIntegerMonthFlag=false;
        Date tempDate2;
        while(true){
            Calendar calendar = new GregorianCalendar();
            calendar.setTime(tempDate);
            int monthDays = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
            tempDate2=DateUtils.addDays(tempDate,monthDays-1);
            if(Long.parseLong(daySdf.format(tempDate2))>Long.parseLong(daySdf.format(endDate))){
                notIntegerMonthFlag=true;
                break;
            }else if(Long.parseLong(daySdf.format(tempDate2))==Long.parseLong(daySdf.format(endDate))) {
                monthNum++;
                break;
            }else {
                monthNum++;
                tempDate=DateUtils.addDays(tempDate,monthDays);
            }
        }
        StringBuilder stringBuilder=new StringBuilder();
        if(notIntegerMonthFlag||monthNum%12!=0){
            stringBuilder.append("/");
        }else if(monthNum%12==0){
            stringBuilder.append(monthNum/12);
        }else{
            stringBuilder.append("/");
        }
        return stringBuilder.toString();
    }
    /**
     * @param args
     * @throws ParseException
     */
    public static void main(String[] args) throws ParseException {
        System.out.println(getDateAddDaySubtraction("2023-11-13",30) );
        /*SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date parse = dateFormat.parse("2023-02-01");
        Date parse1 = dateFormat.parse("2023-04-01");
        Integer dateDifference = getDateDifference(parse, parse1);
        System.out.println("dateDifference = " + dateDifference);*/
    }
}
