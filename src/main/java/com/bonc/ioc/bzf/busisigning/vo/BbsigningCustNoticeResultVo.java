package com.bonc.ioc.bzf.busisigning.vo;

import com.bonc.ioc.common.base.vo.McpBaseVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 客户签约通知结果
 *
 * <AUTHOR>
 * @date 2022-06-07
 * @change 2022-06-07 by wtl for init
 */
@ApiModel(value = "BbsigningCustNoticeResultVo", description = "客户签约通知结果")
@Data
public class BbsigningCustNoticeResultVo extends McpBaseVo implements Serializable {
    private static final long serialVersionUID = 172909321167371409L;
    /**
     * 失败数量
     */
    @ApiModelProperty(value = "失败数量")
    private Integer failCount;

    /**
     * 失败列表
     */
    @ApiModelProperty(value = "失败列表")
    private List<String> failList;

    public BbsigningCustNoticeResultVo() {
        this.failCount = 0;
        this.failList = null;
    }
}
