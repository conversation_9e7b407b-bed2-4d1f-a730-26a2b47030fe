package com.bonc.ioc.bzf.busisigning.feign.vo;

import com.bonc.ioc.common.dict.util.McpDictPoint;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 签约计划导入记录 实体类
 *
 * <AUTHOR>
 * @date 2023-05-23
 * @change 2023-05-23 by 宋鑫 for init
 */
@ApiModel(value="BbsContractImportRecordPageResultVo对象", description="签约计划导入记录")
@Data
public class BbsContractImportRecordPageResultVo extends BasePageResultVo implements Serializable{


    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @NotBlank(message = "主键不能为空",groups = {UpdateValidatorGroup.class})
                                  private String cirId;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称")
                            private String fileName;

    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型")
                            private String fileType;

    /**
     * 签约计划表id
     */
    @ApiModelProperty(value = "签约计划表id")
                            private String signPlanId;

    /**
     * 签约id
     */
    @ApiModelProperty(value = "签约id")
                            private String signId;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
                            private Integer delFlag;

    @ApiModelProperty(value = "签约计划名称")
    private String planName;

    @ApiModelProperty(value = "前端传的签约类型，例如：1-1")
    private String signTypeStr;

    @ApiModelProperty(value = "产品类型(1.公租房 2.包租房)")
    @McpDictPoint(dictCode ="PRODUCT_TYPE",overTransCopyTo="productTypeName")
    private String productType;

    @ApiModelProperty(value = "产品类型名称(1.公租房 2.包租房)")
    private String productTypeName;

    @ApiModelProperty(value = "签约类型(1.散租 2.趸租 3.管理协议)")
    @McpDictPoint(dictCode ="SIGN_TYPE",overTransCopyTo="signTypeName")
    private String signType;

    @ApiModelProperty(value = "签约类型名称(1.散租 2.趸租 3.管理协议)")
    private String signTypeName;

    public String getSignPlanType(){
        if(StringUtils.isNotBlank(productTypeName) && StringUtils.isNotBlank(signTypeName)){
            return productTypeName + "-" + signTypeName;
        }
        return "";
    }

}
