package com.bonc.ioc.bzf.busisigning.vo;

import com.bonc.ioc.bzf.utils.common.convert.CopyFieldPoint;
import com.bonc.ioc.bzf.utils.common.convert.CopyFieldUtil;
import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.bonc.ioc.common.validator.inf.InsertValidatorGroup;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 人-产品：客户表 实体类
 *
 * <AUTHOR>
 * @date 2023-09-05
 * @change 2023-09-05 by liwenqiang for init
 */
@Data
@ApiModel(value = "BbsResultCustomerVo对象", description = "人-产品：客户表")
public class BbsResultCustomerVo extends McpBaseVo implements Serializable {

    @NotBlank(message = "不能为空", groups = {UpdateValidatorGroup.class})
    private String rcId;

    /**
     * 人房关系中间表（弃用）
     */
    @ApiModelProperty(value = "人房关系中间表（弃用）")
    private String relationId;

    /**
     * 人-产品关系外键
     */
    @ApiModelProperty(value = "人-产品关系外键")
    private String rrId;

    /**
     * 租户方：乙、丙、丁（弃用）
     */
    @ApiModelProperty(value = "租户方：乙、丙、丁（弃用）")
    private String type;

    /**
     * 散户或者企业编号
     */
    @ApiModelProperty(value = "散户或者企业编号")
    @CopyFieldPoint(fieldName = "orgCustomerId",type= CopyFieldUtil.TYPE_CUSTOMER)
    @NotBlank(message = "企业id不能为空",groups = {InsertValidatorGroup.class})
    private String customerNo;

    /**
     * 散户或者企业名称
     */
    @ApiModelProperty(value = "散户或者企业名称")
    @CopyFieldPoint(fieldName = "orgCustomerName",type= CopyFieldUtil.TYPE_CUSTOMER)
    @NotBlank(message = "企业名称不能为空",groups = {InsertValidatorGroup.class})
    private String customerName;

    /**
     * 00:个人  01：企业
     */
    @ApiModelProperty(value = "00:个人  01：企业")
    private String customerType;

    /**
     * 租户客商编号
     */
    @ApiModelProperty(value = "租户客商编号")
    private String customerSupplierNo;

    /**
     * 缴费占比（0-1）
     */
    @ApiModelProperty(value = "缴费占比（0-1）")
    private Float customerRatio;

    /**
     * 社会统一信息用代码(企业使用)
     */
    @ApiModelProperty(value = "社会统一信息用代码(企业使用)")
    @CopyFieldPoint(fieldName = "creditCode",type= CopyFieldUtil.TYPE_CUSTOMER)
    @NotBlank(message = "社会统一信息用代码不能为空",groups = {InsertValidatorGroup.class})
    private String customerCreditCode;

    /**
     * 证件类型
     */
    @ApiModelProperty(value = "证件类型")
    private String customerIdType;

    /**
     * 证件号码
     */
    @ApiModelProperty(value = "证件号码")
    private String customerIdNumber;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private String customerGender;

    /**
     * 单位电话
     */
    @ApiModelProperty(value = "单位电话")
    private String customerWorkTel;

    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    private String contactName;

    /**
     * 租户电话
     */
    @ApiModelProperty(value = "租户电话")
    private String customerTel;

    /**
     * 公租房备案号（弃用）
     */
    @ApiModelProperty(value = "公租房备案号（弃用）")
    private String customerPublicRecordNo;

    /**
     * 源住址
     */
    @ApiModelProperty(value = "源住址")
    private String customerHouseAddress;

    /**
     * 开户银行编码
     */
    @ApiModelProperty(value = "开户银行编码")
    private String bankNameCode;

    /**
     * 开户银行
     */
    @ApiModelProperty(value = "开户银行")
    private String bankName;

    /**
     * 银行卡号
     */
    @ApiModelProperty(value = "银行卡号")
    private String bankCard;

    /**
     * 开户行手机号
     */
    @ApiModelProperty(value = "开户行手机号")
    private String bankPhone;

    /**
     * 是否鉴权（1 是0 否）（弃用）
     */
    @ApiModelProperty(value = "是否鉴权（1 是0 否）（弃用）")
    private String bankIsAuthentication;

    /**
     * 是否签署代扣代缴协议（1 是0 否）（弃用）
     */
    @ApiModelProperty(value = "是否签署代扣代缴协议（1 是0 否）（弃用）")
    private String bankIsAgreement;

    /**
     * 协议编号（弃用）
     */
    @ApiModelProperty(value = "协议编号（弃用）")
    private String bankAgreementNo;

    /**
     * 签署类型（1 手机号开通 2 线下开通）（弃用）
     */
    @ApiModelProperty(value = "签署类型（1 手机号开通 2 线下开通）（弃用）")
    private String bankAgreementType;

    /**
     * 支行编码
     */
    @ApiModelProperty(value = "支行编码")
    private String bankSubbranchCode;

    /**
     * 支行名称
     */
    @ApiModelProperty(value = "支行名称")
    private String bankSubbranchName;

    /**
     * 银行分类代码
     */
    @ApiModelProperty(value = "银行分类代码")
    private String bankNccCategoryCode;

    /**
     * 开户名称
     */
    @ApiModelProperty(value = "开户名称")
    private String bankUserName;

    /**
     * 开通代扣代缴服务资料地址（弃用）
     */
    @ApiModelProperty(value = "开通代扣代缴服务资料地址（弃用）")
    private String bankDataUrls;

    /**
     * 电子邮箱地址
     */
    @ApiModelProperty(value = "电子邮箱地址")
    @CopyFieldPoint(fieldName = "email",type= CopyFieldUtil.TYPE_CUSTOMER,defaultValue = "无")
    private String emailAddress;

    /**
     * 通讯地址
     */
    @ApiModelProperty(value = "通讯地址")
    @CopyFieldPoint(fieldName = "storeLocatedAddress",type= CopyFieldUtil.TYPE_CUSTOMER,defaultValue = "无")
    private String mailAddress;

    /**
     * 公司注册地址
     */
    @ApiModelProperty(value = "公司注册地址")
    @CopyFieldPoint(fieldName = "registeredFullAddress",type= CopyFieldUtil.TYPE_CUSTOMER,defaultValue = "无")
    private String registeredAddress;

    /**
     * 邮政编码
     */
    @ApiModelProperty(value = "邮政编码")
    private String postalCode;

    /**
     * 法定代表人
     */
    @ApiModelProperty(value = "法定代表人")
    @CopyFieldPoint(fieldName = "legalRepresentative",type= CopyFieldUtil.TYPE_CUSTOMER)
    @NotBlank(message = "法定代表人不能为空",groups = {InsertValidatorGroup.class})
    private String legalName;

    /**
     * 法定代表人联系电话
     */
    @ApiModelProperty(value = "法定代表人联系电话")
    private String legalMobile;

    /**
     * 委托代理人姓名
     */
    @ApiModelProperty(value = "委托代理人姓名")
    private String consignorName;

    /**
     * 委托代理人联系电话
     */
    @ApiModelProperty(value = "委托代理人联系电话")
    private String consignorMobile;

    /**
     * 委托人与签约方关系(1.朋友 2.亲属)
     */
    @ApiModelProperty(value = "委托人与签约方关系(1.朋友 2.亲属)")
    private String consignorSignatoryRelationType;

    /**
     * 委托人证件类型(1.身份证 2.户口本)
     */
    @ApiModelProperty(value = "委托人证件类型(1.身份证 2.户口本)")
    private String consignorCertificationType;

    /**
     * 委托人证件号码
     */
    @ApiModelProperty(value = "委托人证件号码")
    private String consignorIdvoCard;

    /**
     * 委托人资料地址
     */
    @ApiModelProperty(value = "委托人资料地址")
    private String consignorDataUrls;

    /**
     * 委托人照片地址
     */
    @ApiModelProperty(value = "委托人照片地址")
    private String consignorIdvoCardPhotoUrl;

    /**
     * 办理人照片地址
     */
    @ApiModelProperty(value = "办理人照片地址")
    private String transactorIdvoCardPhotoUrl;

    /**
     * 委托人身份证正面照片（弃用）
     */
    @ApiModelProperty(value = "委托人身份证正面照片（弃用）")
    private String consignorIdvoCardFrontPhotoUrl;

    /**
     * 委托人身份证背面面照片（弃用）
     */
    @ApiModelProperty(value = "委托人身份证背面面照片（弃用）")
    private String consignorIdvoCardBackPhotoUrl;

    /**
     * 办理人身份证正面照片（弃用）
     */
    @ApiModelProperty(value = "办理人身份证正面照片（弃用）")
    private String transactorIdvoCardFrontPhotoUrl;

    /**
     * 办理人身份证背面照片（弃用）
     */
    @ApiModelProperty(value = "办理人身份证背面照片（弃用）")
    private String transactorIdvoCardBackPhotoUrl;

    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    @NotNull(message = "是否删除不能为空", groups = {UpdateValidatorGroup.class})
    private Integer delFlag;

    /**
     * 租户客户编号
     */
    @ApiModelProperty(value = "租户客户编号")
    private String tenantCustomerNo;

    /**
     * 租户客商编号
     */
    @ApiModelProperty(value = "租户客商编号")
    private String tenantSupplierNo;

    /**
     * 租户客商名称
     */
    @ApiModelProperty(value = "租户客商名称")
    private String tenantSupplierName;

    /**
     * 企业客商编号
     */
    @ApiModelProperty(value = "企业客商编号")
    //@CopyFieldPoint(fieldName = "oldCode",type= CopyFieldUtil.TYPE_CUSTOMER)
    @NotBlank(message = "企业客商编号（NCC编码）不能为空",groups = {InsertValidatorGroup.class})
    private String companySupplierNo;

    /**
     * 企业客商名称
     */
    @ApiModelProperty(value = "企业客商名称")
    @CopyFieldPoint(fieldName = "orgCustomerName",type= CopyFieldUtil.TYPE_CUSTOMER)
    @NotBlank(message = "散户或者企业名称不能为空",groups = {InsertValidatorGroup.class})
    private String companySupplierName;

    /**
     * 企业证照类型
     */
    @ApiModelProperty(value = "企业证照类型")
    private String companyIdType;

    /**
     * 缴费金额
     */
    @ApiModelProperty(value = "缴费金额")
    private String customerPaymentAmount;

    /**
     * 国籍
     */
    @ApiModelProperty(value = "国籍")
    private String customerNationality;

    /**
     * 营业执照
     */
    @ApiModelProperty(value = "营业执照")
    private String businessLicense;

    /**
     * 申请业态
     */
    @ApiModelProperty(value = "申请业态")
    private String applyBusinessType;

    /**
     * 申请业态名称
     */
    @ApiModelProperty(value = "申请业态名称")
    private String applyBusinessTypeName;

    /**
     * 意向登记主键id
     */
    @ApiModelProperty(value = "意向登记主键id")
    private String intentionId;

    /**
     * 意向登记主键id列表(字符串逗号分隔)
     */
    @ApiModelProperty(value = "意向登记主键id列表(字符串逗号分隔)")
    private String intentionIdList;
}
