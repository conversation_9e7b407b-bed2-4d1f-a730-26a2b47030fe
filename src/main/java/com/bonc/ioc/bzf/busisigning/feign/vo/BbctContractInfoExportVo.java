package com.bonc.ioc.bzf.busisigning.feign.vo;

import com.bonc.ioc.bzf.busisigning.utils.ExcelField;
import com.bonc.ioc.common.base.vo.McpBaseVo;
import com.bonc.ioc.common.dict.util.McpDictPoint;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 所有的合同信息
 *
 * <AUTHOR>
 * @date 2023-03-30
 */
@Data
@ApiModel(value="BbctContractInfoExportVo对象", description="部分指定的合同信息")
public class BbctContractInfoExportVo extends McpBaseVo implements Serializable{

    @ApiModelProperty(value = "合同号")
    @ExcelField(title="合同号", align = 2, sort = 1)
    private String contractNo;

    @ApiModelProperty(value = "项目名称")
    @ExcelField(title="项目名称", align = 2, sort = 2)
    private String projectName;

    @ApiModelProperty(value = "合同状态")
    @ExcelField(title="合同状态", align = 2, sort = 3)
    private String contractStatusName;

    @ApiModelProperty(value = "合同名称")
    @ExcelField(title="合同名称", align = 2, sort = 4)
    private String a1 = "";

    @ApiModelProperty(value = "合同类型")
    @ExcelField(title="合同类型", align = 2, sort = 5)
    private String contractTypeName;

    @ApiModelProperty(value = "房产名称")
    @ExcelField(title="房产名称", align = 2, sort = 6)
    private String houseResource;

    @ApiModelProperty(value = "客户姓名1")
    @ExcelField(title="客户姓名1", align = 2, sort = 7)
    private String xmName;

    @ApiModelProperty(value = "性别code")
    @McpDictPoint(dictCode = "SEX",overTransCopyTo = "sexValue")
    private String sex;

    @ApiModelProperty(value = "性别name")
    @ExcelField(title="性别", align = 2, sort = 8)
    private String sexValue;

    @ApiModelProperty(value = "证件号码")
    @ExcelField(title="证件号码", align = 2, sort = 9)
    private String certificateNum;

    @ApiModelProperty(value = "公租房备案号")
    @ExcelField(title="公租房备案号", align = 2, sort = 10)
    private String publicRecordNo;

    @ApiModelProperty(value = "租金支付方式Code")
    @McpDictPoint(dictCode = "PAYMENT_CYCLE",overTransCopyTo = "paymentCycleName")
    private String paymentCycleCode;

    @ApiModelProperty(value = "租金支付方式name")
    @ExcelField(title="租金支付方式", align = 2, sort = 11)
    private String paymentCycleName;

    @ApiModelProperty(value = "租金对应的收费项目")
    @ExcelField(title="租金对应的收费项目", align = 2, sort = 12)
    private String paymentProject = "房屋租金";

    @ApiModelProperty(value = "合同起始日")
    @ExcelField(title="合同起始日", align = 2, sort = 13)
    private String rentBeginTime;

    @ApiModelProperty(value = "合同终止日")
    @ExcelField(title="合同终止日", align = 2, sort = 14)
    private String rentEndTime;

    @ApiModelProperty(value = "租赁面积")
    @ExcelField(title="租赁面积", align = 2, sort = 15)
    private String houseArea;

    @ApiModelProperty(value = "签约日期")
    @ExcelField(title="签约日期", align = 2, sort = 16)
    private String signTime;

    @ApiModelProperty(value = "是否报盘")
    @ExcelField(title="是否报盘", align = 2, sort = 17)
    private String isReport = "是";

    @ApiModelProperty(value = "租金标准")
    @ExcelField(title="租金标准", align = 2, sort = 18)
    private String rentStandard;

    @ApiModelProperty(value = "退场日期")
    @ExcelField(title="退场日期", align = 2, sort = 19)
    private String terminateTime;

    @ApiModelProperty(value = "月房屋租金")
    @ExcelField(title="月房屋租金", align = 2, sort = 20)
    private String rent;

    @ApiModelProperty(value = "月增值房屋租金")
    @ExcelField(title="月增值房屋租金", align = 2, sort = 21)
    private String a9 = "";

    @ApiModelProperty(value = "押金")
    @ExcelField(title="押金", align = 2, sort = 22)
    private String cashPledgePrice;

    @ApiModelProperty(value = "开户银行")
    @ExcelField(title="开户银行", align = 2, sort = 23)
    private String bankName;

    @ApiModelProperty(value = "缴费帐号")
    @ExcelField(title="缴费帐号", align = 2, sort = 24)
    private String bankCard;

    @ApiModelProperty(value = "区县")
    @ExcelField(title="区县", align = 2, sort = 25)
    private String a2 = "";

    @ApiModelProperty(value = "街道")
    @ExcelField(title="街道", align = 2, sort = 26)
    private String a3 = "";

    @ApiModelProperty(value = "补贴比例")
    @ExcelField(title="补贴比例", align = 2, sort = 27)
    private String a4 = "";

    @ApiModelProperty(value = "租金标准类型")
    @ExcelField(title="租金标准类型", align = 2, sort = 28)
    private String type = "公租租金标准";

    @ApiModelProperty(value = "租金标准金额")
    @ExcelField(title="租金标准金额", align = 2, sort = 29)
    private String rentTwo;

    @ApiModelProperty(value = "是否无房职工")
    @ExcelField(title="是否无房职工", align = 2, sort = 30)
    private String a5 = "";

    @ApiModelProperty(value = "调配依据")
    @ExcelField(title="调配依据", align = 2, sort = 31)
    private String a6 = "";

    @ApiModelProperty(value = "调配途径")
    @ExcelField(title="调配途径", align = 2, sort = 32)
    private String a7 = "";

    @ApiModelProperty(value = "客户名称2")
    @ExcelField(title="客户名称2", align = 2, sort = 33)
    private String a8 = "";

    @ApiModelProperty(value = "审批时间")
    @ExcelField(title="审批时间", align = 2, sort = 34)
    private String signTime2;

    @ApiModelProperty(value = "手机号码")
    @ExcelField(title="手机号码", align = 2, sort = 35)
    private String mobile;

}
