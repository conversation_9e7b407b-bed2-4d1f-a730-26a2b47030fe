package com.bonc.ioc.bzf.busisigning.feign.vo;

import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 签约计划导入记录 实体类
 *
 * <AUTHOR>
 * @date 2023-05-06
 * @change 2023-05-06 by 宋鑫 for init
 */
@ApiModel(value="BbsPreposeImportRecordPageResultVo对象", description="签约计划导入记录")
public class BbsPreposeImportRecordPageResultVo extends BaseVo implements Serializable{


    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @NotBlank(message = "主键不能为空",groups = {UpdateValidatorGroup.class})
                                  private String pirId;

    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称")
                            private String fileName;

    /**
     * 人房关系
     */
    @ApiModelProperty(value = "人房关系")
                            private String humanRoomRelationship;

    /**
     * 签约计划表id
     */
    @ApiModelProperty(value = "签约计划表id")
                            private String signPlanId;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
                            private Integer delFlag;


    /**
     * 签约计划名称
     */
    @ApiModelProperty(value = "签约计划名称")
    private String signTypeName;

    /**
     * 计划名称
     */
    @ApiModelProperty(value = "计划名称")
    private String planName;

    public String getPlanName() {
        return planName;
    }

    public void setPlanName(String planName) {
        this.planName = planName;
    }

    public String getSignTypeName() {
        return signTypeName;
    }

    public void setSignTypeName(String signTypeName) {
        this.signTypeName = signTypeName;
    }

    /**
     * @return 主键
     */
    public String getPirId() {
        return pirId;
    }

    public void setPirId(String pirId) {
        this.pirId = pirId;
    }

    /**
     * @return 文件名称
     */
    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    /**
     * @return 人房关系
     */
    public String getHumanRoomRelationship() {
        return humanRoomRelationship;
    }

    public void setHumanRoomRelationship(String humanRoomRelationship) {
        this.humanRoomRelationship = humanRoomRelationship;
    }

    /**
     * @return 签约计划表id
     */
    public String getSignPlanId() {
        return signPlanId;
    }

    public void setSignPlanId(String signPlanId) {
        this.signPlanId = signPlanId;
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

      @Override
    public String toString() {
        return "BbsPreposeImportRecordPageResultVo{" +
            "pirId=" + pirId +
            ", fileName=" + fileName +
            ", humanRoomRelationship=" + humanRoomRelationship +
            ", signPlanId=" + signPlanId +
            ", delFlag=" + delFlag +
        "}";
    }
}
