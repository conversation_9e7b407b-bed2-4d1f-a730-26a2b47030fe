package com.bonc.ioc.bzf.busisigning.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 缴费周期变更账单JSON存储VO类
 * 用于在数据库JSON字段中存储精简的账单信息
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@ApiModel(value = "BbsPaymentCycleBillJsonVo对象", description = "缴费周期变更账单JSON存储VO")
public class BbsPaymentCycleBillJsonVo implements Serializable {

    @ApiModelProperty(value = "账单ID")
    private String billId;

    @ApiModelProperty(value = "账单编号")
    private String billNo;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "房源地址")
    private String houseName;

    @ApiModelProperty(value = "账单周期")
    private String billCycle;

    @ApiModelProperty(value = "应缴费开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String payableStartDate;

    @ApiModelProperty(value = "应缴费结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private String payableEndDate;

    @ApiModelProperty(value = "应缴金额")
    private BigDecimal shouldPayAmount;

    @ApiModelProperty(value = "账单状态")
    private String status;

    @ApiModelProperty(value = "账单状态名称")
    private String statusName;

    @ApiModelProperty(value = "租户名称")
    private String tenantName;

    @ApiModelProperty(value = "收费科目")
    private String billChargeSubject;

    @ApiModelProperty(value = "收费科目名称")
    private String billChargeSubjectName;

    // Getter and Setter methods

    public String getBillId() {
        return billId;
    }

    public void setBillId(String billId) {
        this.billId = billId;
    }

    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    public String getHouseName() {
        return houseName;
    }

    public void setHouseName(String houseName) {
        this.houseName = houseName;
    }

    public String getBillCycle() {
        return billCycle;
    }

    public void setBillCycle(String billCycle) {
        this.billCycle = billCycle;
    }

    public String getPayableStartDate() {
        return payableStartDate;
    }

    public void setPayableStartDate(String payableStartDate) {
        this.payableStartDate = payableStartDate;
    }

    public String getPayableEndDate() {
        return payableEndDate;
    }

    public void setPayableEndDate(String payableEndDate) {
        this.payableEndDate = payableEndDate;
    }

    public BigDecimal getShouldPayAmount() {
        return shouldPayAmount;
    }

    public void setShouldPayAmount(BigDecimal shouldPayAmount) {
        this.shouldPayAmount = shouldPayAmount;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatusName() {
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getTenantName() {
        return tenantName;
    }

    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }

    public String getBillChargeSubject() {
        return billChargeSubject;
    }

    public void setBillChargeSubject(String billChargeSubject) {
        this.billChargeSubject = billChargeSubject;
    }

    public String getBillChargeSubjectName() {
        return billChargeSubjectName;
    }

    public void setBillChargeSubjectName(String billChargeSubjectName) {
        this.billChargeSubjectName = billChargeSubjectName;
    }

    @Override
    public String toString() {
        return "BbsPaymentCycleBillJsonVo{" +
                "billId='" + billId + '\'' +
                ", billNo='" + billNo + '\'' +
                ", contractCode='" + contractCode + '\'' +
                ", houseName='" + houseName + '\'' +
                ", billCycle='" + billCycle + '\'' +
                ", payableStartDate='" + payableStartDate + '\'' +
                ", payableEndDate='" + payableEndDate + '\'' +
                ", shouldPayAmount=" + shouldPayAmount +
                ", status='" + status + '\'' +
                ", statusName='" + statusName + '\'' +
                ", tenantName='" + tenantName + '\'' +
                ", billChargeSubject='" + billChargeSubject + '\'' +
                ", billChargeSubjectName='" + billChargeSubjectName + '\'' +
                '}';
    }
} 