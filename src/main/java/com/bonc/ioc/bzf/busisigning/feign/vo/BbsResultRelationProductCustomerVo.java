package com.bonc.ioc.bzf.busisigning.feign.vo;

import com.bonc.ioc.bzf.utils.common.convert.CopyFieldPoint;
import com.bonc.ioc.bzf.utils.common.convert.CopyFieldUtil;
import com.bonc.ioc.common.validator.inf.UpdateValidatorGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 人-产品：关系表 实体类
 *
 * <AUTHOR>
 * @date 2023-05-08
 * @change 2023-05-08 by 宋鑫 for init
 */
@ApiModel(value="BbsResultRelationProductCustomerVo", description="人产品集合")
public class BbsResultRelationProductCustomerVo implements Serializable{


    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @NotBlank(message = "主键不能为空",groups = {UpdateValidatorGroup.class})
    private String rrId;

    /**
     * 客户或者企业唯一标识
     */
    @ApiModelProperty(value = "客户或者企业唯一标识")
    private String masterTenantId;

    /**
     * 产品编号
     */
    @ApiModelProperty(value = "产品编号")
    private String productNo;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNo;

    /**
     * 来源:1：选房中心 ，2：入住中心，3、配租中心，4、续租中心
     */
    @ApiModelProperty(value = "来源:1：选房中心 ，2：入住中心，3、配租中心，4、续租中心")
    private Integer source;

    /**
     * 签约计划ID
     */
    @ApiModelProperty(value = "签约计划ID")
    private String signPlanId;

    /**
     * 签约结果ID
     */
    @ApiModelProperty(value = "签约结果ID")
    private String signInfoId;

    /**
     * XX计划ID
     */
    @ApiModelProperty(value = "XX计划ID")
    private String humanRoomPlanId;

    /**
     * 选房计划名称/入住计划/XX计划名称
     */
    @ApiModelProperty(value = "选房计划名称/入住计划/XX计划名称")
    private String humanRoomPlanName;

    /**
     * 选房时间/验房时间/xx时间
     */
    @ApiModelProperty(value = "选房时间/验房时间/xx时间")
    private String humanRoomDate;

    /**
     * 批次号：cpyyyymmddXXXX
     */
    @ApiModelProperty(value = "批次号：cpyyyymmddXXXX")
    private String batchNo;

    /**
     * 删除标志;删除标识（1 未删除 0 已删除）
     */
    @ApiModelProperty(value = "删除标志;删除标识（1 未删除 0 已删除）")
    private Integer delFlag;
    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String productName;


    /**
     * 项目编号/运营项目
     */
    @ApiModelProperty(value = "项目编号/运营项目")
    private String projectNo;

    private String projectName;

    /**
     * 租金标准（元/m²/月）
     */
    @ApiModelProperty(value = "租金标准（元/m²/月）")
    private String rentStandardNo;

    /**
     * 租金标准（元/m²/月）名称
     */
    @ApiModelProperty(value = "租金标准（元/m²/月）名称")
    private String rentStandardName;
    /**
     * 拼接租金标准
     */
    @ApiModelProperty(value = "拼接租金标准")
    private String rentStandard;
    /**
     * 选房渠道
     */
    @ApiModelProperty(value = "选房渠道")
    private String houseSelectionChannelsNo;

    /**
     * 选房渠道名称
     */
    @ApiModelProperty(value = "选房渠道名称")
    private String houseSelectionChannelsName;

    /**
     * 小区或楼宇
     */
    @ApiModelProperty(value = "小区或楼宇")
    private String communityBuildingNo;

    /**
     * 小区或楼宇名称
     */
    @ApiModelProperty(value = "小区或楼宇名称")
    private String communityBuildingName;

    /**
     * 组团
     */
    @ApiModelProperty(value = "组团")
    private String groupNo;

    /**
     * 组团名称
     */
    @ApiModelProperty(value = "组团名称")
    private String groupName;

    /**
     * 楼号
     */
    @ApiModelProperty(value = "楼号")
    private String buildingNo;

    /**
     * 楼名称
     */
    @ApiModelProperty(value = "楼名称")
    private String buildingName;

    /**
     * 单元号
     */
    @ApiModelProperty(value = "单元号")
    private String unitNo;

    /**
     * 单元名称
     */
    @ApiModelProperty(value = "单元名称")
    private String unitName;
    /**
     * 户型
     */
    @ApiModelProperty(value = "户型")
    private String houseTypeNo;
    /**
     * 户型名称
     */
    @ApiModelProperty(value = "户型名称")
    private String houseTypeName;
    /**
     * 建筑面积
     */
    @ApiModelProperty(value = "建筑面积")
    private String houseStructArea;

    @ApiModelProperty(value = "套内建筑面积")
    @CopyFieldPoint(fieldName = "innerSleeveArea",type= CopyFieldUtil.TYPE_HOUSE)
    private String innerSleeveArea;
    /**
     * 套型
     */
    @ApiModelProperty(value = "套型")
    private String jacketed;


    /**
     * 租户方：乙、丙、丁
     */
    @ApiModelProperty(value = "租户方：乙、丙、丁")
    private String type;

    /**
     * 散户或者企业编号
     */
    @ApiModelProperty(value = "散户或者企业编号")
    private String customerNo;

    /**
     * 散户或者企业名称
     */
    @ApiModelProperty(value = "散户或者企业名称")
    private String customerName;


    /**
     * 未签约的企业名称
     */
    @ApiModelProperty(value = "未签约的企业名称")
    private String customerNameUnit;


    /**
     * 00:散户  01：企业
     */
    @ApiModelProperty(value = "00:散户  01：企业")
    private String customerType;

    /**
     * 租户客商编号
     */
    @ApiModelProperty(value = "租户客商编号")
    private String customerSupplierNo;

    /**
     * 缴费占比（0-1）
     */
    @ApiModelProperty(value = "缴费占比（0-1）")
    private Float customerRatio;

    /**
     * 社会统一信息用代码(企业使用)
     */
    @ApiModelProperty(value = "社会统一信息用代码(企业使用)")
    private String customerCreditCode;

    /**
     * 证件类型
     */
    @ApiModelProperty(value = "证件类型")
    private String customerIdType;

    private String customerIdTypeName;

    /**
     * 证件号码
     */
    @ApiModelProperty(value = "证件号码")
    private String customerIdNumber;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private String customerGender;

    /**
     * 单位电话
     */
    @ApiModelProperty(value = "单位电话")
    private String customerWorkTel;

    /**
     * 租户电话
     */
    @ApiModelProperty(value = "租户电话")
    private String customerTel;

    /**
     * 公租房备案号
     */
    @ApiModelProperty(value = "公租房备案号")
    private String customerPublicRecordNo;

    /**
     * 源住址
     */
    @ApiModelProperty(value = "源住址")
    private String customerHouseAddress;

    /**
     * 租户客户编号
     */
    @ApiModelProperty(value = "租户客户编号")
    private String tenantCustomerNo;

    /**
     * 租户客商编号
     */
    @ApiModelProperty(value = "租户客商编号")
    private String tenantSupplierNo;

    /**
     * 租户客商名称
     */
    @ApiModelProperty(value = "租户客商名称")
    private String tenantSupplierName;

    /**
     * 趸租企业客商编号
     */
    @ApiModelProperty(value = "趸租企业客商编号")
    private String companySupplierNo;

    /**
     * 趸租企业客商名称
     */
    @ApiModelProperty(value = "趸租企业客商名称")
    private String companySupplierName;

    /**
     * 趸租企业证照类型
     */
    @ApiModelProperty(value = "趸租企业证照类型")
    private String companyIdType;

    /**
     * 缴费金额
     */
    @ApiModelProperty(value = "缴费金额")
    private String customerPaymentAmount;

    /**
     * 国籍
     */
    @ApiModelProperty(value = "国籍")
    private String customerNationality;
    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private String projectId;
    /**
     * 房间名称
     */
    @ApiModelProperty(value = "房间名称")
    private String roomName;

    /**
     * 小区地址
     */
    @ApiModelProperty(value = "小区地址")
    private String communityAddress;

    /**
     * 小区所在区
     */
    @ApiModelProperty(value = "小区所在区")
    private String communityRegion;
    /**
     * 所在层
     */
    @ApiModelProperty(value = "所在层")
    private String currentFloorNo;

    /**
     * 总层数
     */
    @ApiModelProperty(value = "总层数")
    private String totalFloorNo;

    /**
     * 房间朝向
     */
    @ApiModelProperty(value = "房间朝向")
    private String houseOrientation;

    /**
     * 项目简称
     */
    @ApiModelProperty(value = "项目简称")
    private String projectShortName;

    /**
     * 运营主体类型
     */
    @ApiModelProperty(value = "运营主体类型")
    private String operateEntityType;

    /**
     * 运营主体名称
     */
    @ApiModelProperty(value = "运营主体名称")
    private String operateEntityName;

    /**
     * 运营单位业务中台编号
     */
    @ApiModelProperty(value = "运营单位业务中台编号")
    private String operateUnitBusinessNo;

    /**
     * 运营单位编号（NCC）
     */
    @ApiModelProperty(value = "运营单位编号（NCC）")
    private String operateUnitNo;

    /**
     * 运营单位名称
     */
    @ApiModelProperty(value = "运营单位名称")
    private String operateUnitName;

    /**
     * 项目区域业务中台编号
     */
    @ApiModelProperty(value = "项目区域业务中台编号")
    private String projectAreaBusinessNo;

    /**
     * 项目区域编号（NCC）
     */
    @ApiModelProperty(value = "项目区域编号（NCC）")
    private String projectAreaNo;

    /**
     * 项目区域名称
     */
    @ApiModelProperty(value = "项目区域名称")
    private String projectAreaName;

    /**
     * 项目业态
     */
    @ApiModelProperty(value = "项目业态")
    private String projectFormat;

    /**
     * 所在小区或楼宇名称
     */
    @ApiModelProperty(value = "所在小区或楼宇名称")
    private String projectEstate;


    /**
     * 租金
     */
    @ApiModelProperty(value = "租金")
    private Double rent;


    /**
     * 房间号
     */
    @ApiModelProperty(value = "房间号")
    private String houseNo;

    /**
     * 房源编号NCC编码
     */
    @ApiModelProperty(value = "房源编号NCC编码")
    private String houseNoNcc;

    /**
     * 项目编号NCC编码
     */
    @ApiModelProperty(value = "项目编号NCC编码")
    private String projectNoNcc;

    /**
     * 房屋租赁类型 01 公祖， 07 保租
     */
    @ApiModelProperty(value = "房屋租赁类型 01 公祖， 07 保租")
    private String houseHireType;


    /**
     * 来源节点
     */
    @ApiModelProperty(value = "来源节点")
    private String sourceNode;

    @ApiModelProperty(value = "定价策率名称")
    private String priceSideRatioName;

    @ApiModelProperty(value = "月租金规则名称")
    private String monthlyRentRulesName;



    @ApiModelProperty(value = "社会统一信用代码")
    private String customerCreditCodeUnit;

    /**
     * 产品扩展
     */
    @ApiModelProperty(value = "产品扩展")
    private String productExtend;

    public String getCustomerCreditCodeUnit() {
        return customerCreditCodeUnit;
    }

    public void setCustomerCreditCodeUnit(String customerCreditCodeUnit) {
        this.customerCreditCodeUnit = customerCreditCodeUnit;
    }

    public String getPriceSideRatioName() {
        return priceSideRatioName;
    }

    public void setPriceSideRatioName(String priceSideRatioName) {
        this.priceSideRatioName = priceSideRatioName;
    }

    public String getMonthlyRentRulesName() {
        return monthlyRentRulesName;
    }

    public void setMonthlyRentRulesName(String monthlyRentRulesName) {
        this.monthlyRentRulesName = monthlyRentRulesName;
    }

    private String legalNameUnit;

    public String getLegalNameUnit() {
        return legalNameUnit;
    }

    public void setLegalNameUnit(String legalNameUnit) {
        this.legalNameUnit = legalNameUnit;
    }

    public String getRentStandard() {
        return rentStandard;
    }

    public void setRentStandard(String rentStandard) {
        this.rentStandard = rentStandard;
    }

    public String getSourceNode() {
        return sourceNode;
    }

    public void setSourceNode(String sourceNode) {
        this.sourceNode = sourceNode;
    }

    public String getCustomerIdTypeName() {
        return customerIdTypeName;
    }

    public void setCustomerIdTypeName(String customerIdTypeName) {
        this.customerIdTypeName = customerIdTypeName;
    }

    public String getCustomerNationality() {
        return customerNationality;
    }

    public void setCustomerNationality(String customerNationality) {
        this.customerNationality = customerNationality;
    }

    public String getHouseTypeNo() {
        return houseTypeNo;
    }

    public void setHouseTypeNo(String houseTypeNo) {
        this.houseTypeNo = houseTypeNo;
    }

    public String getHouseTypeName() {
        return houseTypeName;
    }

    public void setHouseTypeName(String houseTypeName) {
        this.houseTypeName = houseTypeName;
    }

    public String getHouseStructArea() {
        return houseStructArea;
    }

    public void setHouseStructArea(String houseStructArea) {
        this.houseStructArea = houseStructArea;
    }

    public String getJacketed() {
        return jacketed;
    }

    public void setJacketed(String jacketed) {
        this.jacketed = jacketed;
    }

    public Double getRent() {
        return rent;
    }

    public void setRent(Double rent) {
        this.rent = rent;
    }

    public String getHouseNoNcc() {
        return houseNoNcc;
    }

    public void setHouseNoNcc(String houseNoNcc) {
        this.houseNoNcc = houseNoNcc;
    }

    public String getProjectNoNcc() {
        return projectNoNcc;
    }

    public void setProjectNoNcc(String projectNoNcc) {
        this.projectNoNcc = projectNoNcc;
    }

    public String getHouseHireType() {
        return houseHireType;
    }

    public void setHouseHireType(String houseHireType) {
        this.houseHireType = houseHireType;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getRoomName() {
        return roomName;
    }

    public void setRoomName(String roomName) {
        this.roomName = roomName;
    }

    public String getCommunityAddress() {
        return communityAddress;
    }

    public void setCommunityAddress(String communityAddress) {
        this.communityAddress = communityAddress;
    }

    public String getCommunityRegion() {
        return communityRegion;
    }

    public void setCommunityRegion(String communityRegion) {
        this.communityRegion = communityRegion;
    }

    public String getCurrentFloorNo() {
        return currentFloorNo;
    }

    public void setCurrentFloorNo(String currentFloorNo) {
        this.currentFloorNo = currentFloorNo;
    }

    public String getTotalFloorNo() {
        return totalFloorNo;
    }

    public void setTotalFloorNo(String totalFloorNo) {
        this.totalFloorNo = totalFloorNo;
    }

    public String getHouseOrientation() {
        return houseOrientation;
    }

    public void setHouseOrientation(String houseOrientation) {
        this.houseOrientation = houseOrientation;
    }

    public String getProjectShortName() {
        return projectShortName;
    }

    public void setProjectShortName(String projectShortName) {
        this.projectShortName = projectShortName;
    }

    public String getOperateEntityType() {
        return operateEntityType;
    }

    public void setOperateEntityType(String operateEntityType) {
        this.operateEntityType = operateEntityType;
    }

    public String getOperateEntityName() {
        return operateEntityName;
    }

    public void setOperateEntityName(String operateEntityName) {
        this.operateEntityName = operateEntityName;
    }

    public String getOperateUnitBusinessNo() {
        return operateUnitBusinessNo;
    }

    public void setOperateUnitBusinessNo(String operateUnitBusinessNo) {
        this.operateUnitBusinessNo = operateUnitBusinessNo;
    }

    public String getOperateUnitNo() {
        return operateUnitNo;
    }

    public void setOperateUnitNo(String operateUnitNo) {
        this.operateUnitNo = operateUnitNo;
    }

    public String getOperateUnitName() {
        return operateUnitName;
    }

    public void setOperateUnitName(String operateUnitName) {
        this.operateUnitName = operateUnitName;
    }

    public String getProjectAreaBusinessNo() {
        return projectAreaBusinessNo;
    }

    public void setProjectAreaBusinessNo(String projectAreaBusinessNo) {
        this.projectAreaBusinessNo = projectAreaBusinessNo;
    }

    public String getProjectAreaNo() {
        return projectAreaNo;
    }

    public void setProjectAreaNo(String projectAreaNo) {
        this.projectAreaNo = projectAreaNo;
    }

    public String getProjectAreaName() {
        return projectAreaName;
    }

    public void setProjectAreaName(String projectAreaName) {
        this.projectAreaName = projectAreaName;
    }

    public String getProjectFormat() {
        return projectFormat;
    }

    public void setProjectFormat(String projectFormat) {
        this.projectFormat = projectFormat;
    }

    public String getProjectEstate() {
        return projectEstate;
    }

    public void setProjectEstate(String projectEstate) {
        this.projectEstate = projectEstate;
    }

    public String getTenantCustomerNo() {
        return tenantCustomerNo;
    }

    public void setTenantCustomerNo(String tenantCustomerNo) {
        this.tenantCustomerNo = tenantCustomerNo;
    }

    public String getTenantSupplierNo() {
        return tenantSupplierNo;
    }

    public void setTenantSupplierNo(String tenantSupplierNo) {
        this.tenantSupplierNo = tenantSupplierNo;
    }

    public String getTenantSupplierName() {
        return tenantSupplierName;
    }

    public void setTenantSupplierName(String tenantSupplierName) {
        this.tenantSupplierName = tenantSupplierName;
    }

    public String getCompanySupplierNo() {
        return companySupplierNo;
    }

    public void setCompanySupplierNo(String companySupplierNo) {
        this.companySupplierNo = companySupplierNo;
    }

    public String getCompanySupplierName() {
        return companySupplierName;
    }

    public void setCompanySupplierName(String companySupplierName) {
        this.companySupplierName = companySupplierName;
    }

    public String getCompanyIdType() {
        return companyIdType;
    }

    public void setCompanyIdType(String companyIdType) {
        this.companyIdType = companyIdType;
    }

    public String getCustomerPaymentAmount() {
        return customerPaymentAmount;
    }

    public void setCustomerPaymentAmount(String customerPaymentAmount) {
        this.customerPaymentAmount = customerPaymentAmount;
    }

    public String getCustomerNameUnit() {
        return customerNameUnit;
    }

    public void setCustomerNameUnit(String customerNameUnit) {
        this.customerNameUnit = customerNameUnit;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProjectNo() {
        return projectNo;
    }

    public void setProjectNo(String projectNo) {
        this.projectNo = projectNo;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getRentStandardNo() {
        return rentStandardNo;
    }

    public void setRentStandardNo(String rentStandardNo) {
        this.rentStandardNo = rentStandardNo;
    }

    public String getRentStandardName() {
        return rentStandardName;
    }

    public void setRentStandardName(String rentStandardName) {
        this.rentStandardName = rentStandardName;
    }

    public String getHouseSelectionChannelsNo() {
        return houseSelectionChannelsNo;
    }

    public void setHouseSelectionChannelsNo(String houseSelectionChannelsNo) {
        this.houseSelectionChannelsNo = houseSelectionChannelsNo;
    }

    public String getHouseSelectionChannelsName() {
        return houseSelectionChannelsName;
    }

    public void setHouseSelectionChannelsName(String houseSelectionChannelsName) {
        this.houseSelectionChannelsName = houseSelectionChannelsName;
    }

    public String getCommunityBuildingNo() {
        return communityBuildingNo;
    }

    public void setCommunityBuildingNo(String communityBuildingNo) {
        this.communityBuildingNo = communityBuildingNo;
    }

    public String getCommunityBuildingName() {
        return communityBuildingName;
    }

    public void setCommunityBuildingName(String communityBuildingName) {
        this.communityBuildingName = communityBuildingName;
    }

    public String getGroupNo() {
        return groupNo;
    }

    public void setGroupNo(String groupNo) {
        this.groupNo = groupNo;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getBuildingNo() {
        return buildingNo;
    }

    public void setBuildingNo(String buildingNo) {
        this.buildingNo = buildingNo;
    }

    public String getBuildingName() {
        return buildingName;
    }

    public void setBuildingName(String buildingName) {
        this.buildingName = buildingName;
    }

    public String getUnitNo() {
        return unitNo;
    }

    public void setUnitNo(String unitNo) {
        this.unitNo = unitNo;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getRrId() {
        return rrId;
    }

    public void setRrId(String rrId) {
        this.rrId = rrId;
    }

    public String getCustomerNo() {
        return customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    public String getCustomerSupplierNo() {
        return customerSupplierNo;
    }

    public void setCustomerSupplierNo(String customerSupplierNo) {
        this.customerSupplierNo = customerSupplierNo;
    }

    public Float getCustomerRatio() {
        return customerRatio;
    }

    public void setCustomerRatio(Float customerRatio) {
        this.customerRatio = customerRatio;
    }

    public String getCustomerCreditCode() {
        return customerCreditCode;
    }

    public void setCustomerCreditCode(String customerCreditCode) {
        this.customerCreditCode = customerCreditCode;
    }

    public String getCustomerIdType() {
        return customerIdType;
    }

    public void setCustomerIdType(String customerIdType) {
        this.customerIdType = customerIdType;
    }

    public String getCustomerIdNumber() {
        return customerIdNumber;
    }

    public void setCustomerIdNumber(String customerIdNumber) {
        this.customerIdNumber = customerIdNumber;
    }

    public String getCustomerGender() {
        return customerGender;
    }

    public void setCustomerGender(String customerGender) {
        this.customerGender = customerGender;
    }

    public String getCustomerWorkTel() {
        return customerWorkTel;
    }

    public void setCustomerWorkTel(String customerWorkTel) {
        this.customerWorkTel = customerWorkTel;
    }

    public String getCustomerTel() {
        return customerTel;
    }

    public void setCustomerTel(String customerTel) {
        this.customerTel = customerTel;
    }

    public String getCustomerPublicRecordNo() {
        return customerPublicRecordNo;
    }

    public void setCustomerPublicRecordNo(String customerPublicRecordNo) {
        this.customerPublicRecordNo = customerPublicRecordNo;
    }

    public String getCustomerHouseAddress() {
        return customerHouseAddress;
    }

    public void setCustomerHouseAddress(String customerHouseAddress) {
        this.customerHouseAddress = customerHouseAddress;
    }

    /**
     * @return 客户或者企业唯一标识
     */
    public String getMasterTenantId() {
        return masterTenantId;
    }

    public void setMasterTenantId(String masterTenantId) {
        this.masterTenantId = masterTenantId;
    }

    /**
     * @return 产品编号
     */
    public String getProductNo() {
        return productNo;
    }

    public void setProductNo(String productNo) {
        this.productNo = productNo;
    }

    /**
     * @return 合同编号
     */
    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    /**
     * @return 来源:1：选房中心 ，2：入住中心，3、配租中心，4、续租中心
     */
    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    /**
     * @return 签约计划ID
     */
    public String getSignPlanId() {
        return signPlanId;
    }

    public void setSignPlanId(String signPlanId) {
        this.signPlanId = signPlanId;
    }

    /**
     * @return 签约结果ID
     */
    public String getSignInfoId() {
        return signInfoId;
    }

    public void setSignInfoId(String signInfoId) {
        this.signInfoId = signInfoId;
    }

    /**
     * @return XX计划ID
     */
    public String getHumanRoomPlanId() {
        return humanRoomPlanId;
    }

    public void setHumanRoomPlanId(String humanRoomPlanId) {
        this.humanRoomPlanId = humanRoomPlanId;
    }

    /**
     * @return 选房计划名称/入住计划/XX计划名称
     */
    public String getHumanRoomPlanName() {
        return humanRoomPlanName;
    }

    public void setHumanRoomPlanName(String humanRoomPlanName) {
        this.humanRoomPlanName = humanRoomPlanName;
    }

    /**
     * @return 选房时间/验房时间/xx时间
     */
    public String getHumanRoomDate() {
        return humanRoomDate;
    }

    public void setHumanRoomDate(String humanRoomDate) {
        this.humanRoomDate = humanRoomDate;
    }

    /**
     * @return 批次号：cpyyyymmddXXXX
     */
    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    /**
     * @return 删除标志;删除标识（1 未删除 0 已删除）
     */
    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public String getProductExtend() {
        return productExtend;
    }

    public void setProductExtend(String productExtend) {
        this.productExtend = productExtend;
    }

    public String getHouseNo() {
        return houseNo;
    }

    public void setHouseNo(String houseNo) {
        this.houseNo = houseNo;
    }


    public String getInnerSleeveArea() {
        return innerSleeveArea;
    }

    public void setInnerSleeveArea(String innerSleeveArea) {
        this.innerSleeveArea = innerSleeveArea;
    }

    @Override
    public String toString() {
        return "BbsResultRelationBusinessPageResultVo{" +
                "rrId=" + rrId +
                ", masterTenantId=" + masterTenantId +
                ", productNo=" + productNo +
                ", contractNo=" + contractNo +
                ", source=" + source +
                ", signPlanId=" + signPlanId +
                ", signInfoId=" + signInfoId +
                ", humanRoomPlanId=" + humanRoomPlanId +
                ", humanRoomPlanName=" + humanRoomPlanName +
                ", humanRoomDate=" + humanRoomDate +
                ", batchNo=" + batchNo +
                ", delFlag=" + delFlag +
                "}";
    }
}
